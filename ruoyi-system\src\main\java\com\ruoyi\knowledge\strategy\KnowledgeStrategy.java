package com.ruoyi.knowledge.strategy;

import com.ruoyi.knowledge.enums.StrategyType;

/**
 * 知识库策略接口
 * 所有知识库策略都需要实现此接口
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
public interface KnowledgeStrategy<T, R> {
    
    /**
     * 获取策略类型
     * 
     * @return 策略类型
     */
    StrategyType getStrategyType();
    
    /**
     * 获取策略名称
     * 
     * @return 策略名称
     */
    String getStrategyName();
    
    /**
     * 获取策略描述
     * 
     * @return 策略描述
     */
    String getStrategyDescription();
    
    /**
     * 执行策略
     * 
     * @param input 输入数据
     * @param config 策略配置
     * @return 执行结果
     * @throws Exception 执行异常
     */
    R execute(T input, String config) throws Exception;
    
    /**
     * 验证策略配置
     * 
     * @param config 策略配置JSON
     * @return 是否有效
     */
    boolean validateConfig(String config);
    
    /**
     * 获取默认配置
     * 
     * @return 默认配置JSON
     */
    String getDefaultConfig();
    
    /**
     * 是否支持并行执行
     * 
     * @return 是否支持并行执行
     */
    default boolean supportParallelExecution() {
        return true;
    }
    
    /**
     * 获取执行优先级（数字越小优先级越高）
     * 
     * @return 优先级
     */
    default int getExecutionPriority() {
        return 100;
    }
}
