<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="会话标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入会话标题"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户名" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入用户名"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="AI模型" prop="model">
        <el-select v-model="queryParams.model" placeholder="请选择AI模型" clearable>
          <el-option label="qwen-plus" value="qwen-plus" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="正常" value="0" />
          <el-option label="停用" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['ai:session:remove']"
        >删除</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="sessionList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="会话ID" align="center" prop="sessionId" width="200" />
      <el-table-column label="会话标题" align="center" prop="title" />
      <el-table-column label="用户名" align="center" prop="userName" />
      <el-table-column label="AI模型" align="center" prop="model" />
      <el-table-column label="消息数量" align="center" prop="messageCount" />
      <el-table-column label="最后活跃时间" align="center" prop="lastActiveTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.lastActiveTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <dict-tag :options="sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" @click="handleView(scope.row)" v-hasPermi="['ai:chat:history']">
            查看消息
          </el-button>
          <el-button link type="primary" @click="handleDelete(scope.row)" v-hasPermi="['ai:session:remove']">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 会话消息查看对话框 -->
    <el-dialog :title="'会话消息 - ' + viewSession.title" v-model="viewOpen" width="800px" append-to-body>
      <div class="message-list" style="max-height: 400px; overflow-y: auto;">
        <div v-for="message in messageList" :key="message.messageId" class="message-item">
          <div class="message-header">
            <span class="message-role" :class="message.role === 'user' ? 'user-role' : 'ai-role'">
              {{ message.role === 'user' ? '用户' : 'AI助手' }}
            </span>
            <span class="message-time">{{ parseTime(message.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </div>
          <div class="message-content">{{ message.content }}</div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="viewOpen = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="AiSession">
import { ref, reactive, toRefs, getCurrentInstance, onMounted } from 'vue'
import { listAiChatSession, getAiChatSession, delAiChatSession } from "@/api/ai/session";
import { getChatHistory } from "@/api/ai/chat";

const { proxy } = getCurrentInstance();
const { sys_normal_disable } = proxy.useDict('sys_normal_disable');

const sessionList = ref([]);
const messageList = ref([]);
const viewOpen = ref(false);
const viewSession = ref({});
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    title: null,
    userName: null,
    model: null,
    status: null
  }
});

const { queryParams } = toRefs(data);

/** 查询AI聊天会话列表 */
function getList() {
  loading.value = true;
  listAiChatSession(queryParams.value).then(response => {
    sessionList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  viewOpen.value = false;
  reset();
}

// 表单重置
function reset() {
  messageList.value = [];
  viewSession.value = {};
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.sessionId)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 查看会话消息 */
function handleView(row) {
  viewSession.value = row;
  getChatHistory({ sessionId: row.sessionId }).then(response => {
    messageList.value = response.data || response.rows || [];
    viewOpen.value = true;
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const sessionIds = row.sessionId || ids.value;
  proxy.$modal.confirm('是否确认删除AI聊天会话编号为"' + sessionIds + '"的数据项？').then(function() {
    return delAiChatSession(sessionIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

onMounted(() => {
  getList();
});
</script>

<style scoped>
.message-item {
  margin-bottom: 15px;
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.message-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 12px;
}

.message-role {
  font-weight: bold;
}

.user-role {
  color: #409eff;
}

.ai-role {
  color: #67c23a;
}

.message-time {
  color: #909399;
}

.message-content {
  line-height: 1.6;
  white-space: pre-wrap;
}
</style>
