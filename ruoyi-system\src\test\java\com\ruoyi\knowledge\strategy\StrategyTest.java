package com.ruoyi.knowledge.strategy;

import com.ruoyi.knowledge.domain.KnowledgeDocument;
import com.ruoyi.knowledge.domain.KnowledgeBase;
import com.ruoyi.knowledge.strategy.initialization.DefaultInitializationStrategy;
import com.ruoyi.knowledge.strategy.initialization.InitializationContext;
import com.ruoyi.knowledge.strategy.initialization.InitializationResult;
import com.ruoyi.knowledge.strategy.segmentation.DefaultRecursiveSegmentationStrategy;
import com.ruoyi.knowledge.strategy.segmentation.SegmentationContext;
import com.ruoyi.knowledge.strategy.segmentation.SegmentationResult;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 策略功能测试类
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
public class StrategyTest {

    @Test
    public void testDefaultInitializationStrategy() {
        // 创建测试数据
        KnowledgeBase knowledgeBase = new KnowledgeBase();
        knowledgeBase.setId(1L);
        knowledgeBase.setName("测试知识库");
        knowledgeBase.setDescription("这是一个测试知识库");

        KnowledgeDocument doc1 = new KnowledgeDocument();
        doc1.setId(1L);
        doc1.setName("测试文档1.txt");
        doc1.setFormat("txt");

        KnowledgeDocument doc2 = new KnowledgeDocument();
        doc2.setId(2L);
        doc2.setName("测试文档2.md");
        doc2.setFormat("md");

        List<KnowledgeDocument> documents = Arrays.asList(doc1, doc2);

        InitializationContext context = new InitializationContext(knowledgeBase, documents, 1L, "admin");

        // 执行策略
        DefaultInitializationStrategy strategy = new DefaultInitializationStrategy();
        
        try {
            InitializationResult result = strategy.execute(context, null);
            
            // 验证结果
            assertNotNull(result);
            assertTrue(result.isSuccess());
            assertEquals(2, result.getProcessedDocuments());
            assertEquals(1L, result.getKnowledgeBaseId());
            assertNotNull(result.getMessage());
            
            System.out.println("初始化策略测试通过: " + result);
            
        } catch (Exception e) {
            fail("初始化策略执行失败: " + e.getMessage());
        }
    }

    @Test
    public void testDefaultRecursiveSegmentationStrategy() {
        // 创建测试数据
        KnowledgeDocument document = new KnowledgeDocument();
        document.setId(1L);
        document.setName("测试文档.txt");
        document.setFormat("txt");

        String content = "这是第一段内容。这段内容比较短。\n\n" +
                        "这是第二段内容。这段内容稍微长一些，包含了更多的信息和细节。\n\n" +
                        "这是第三段内容。这段内容更长，包含了大量的文本信息，用于测试文档分割功能是否能够正确地将长文档分割成合适大小的段落。" +
                        "这里继续添加更多的文本内容，以确保能够触发分割逻辑。";

        SegmentationContext context = new SegmentationContext(document, content, 1L);

        // 执行策略
        DefaultRecursiveSegmentationStrategy strategy = new DefaultRecursiveSegmentationStrategy();
        
        try {
            SegmentationResult result = strategy.execute(context, null);
            
            // 验证结果
            assertNotNull(result);
            assertTrue(result.isSuccess());
            assertNotNull(result.getSegments());
            assertTrue(result.getSegments().size() > 0);
            
            System.out.println("段落切分策略测试通过:");
            System.out.println("原文长度: " + content.length());
            System.out.println("分割段落数: " + result.getSegments().size());
            
            for (int i = 0; i < result.getSegments().size(); i++) {
                SegmentationResult.TextSegment segment = result.getSegments().get(i);
                System.out.println("段落 " + (i + 1) + " (长度: " + segment.getText().length() + "): " + 
                                 segment.getText().substring(0, Math.min(50, segment.getText().length())) + "...");
            }
            
        } catch (Exception e) {
            fail("段落切分策略执行失败: " + e.getMessage());
        }
    }

    @Test
    public void testStrategyConfigValidation() {
        DefaultInitializationStrategy initStrategy = new DefaultInitializationStrategy();
        DefaultRecursiveSegmentationStrategy segStrategy = new DefaultRecursiveSegmentationStrategy();

        // 测试有效配置
        String validConfig1 = initStrategy.getDefaultConfig();
        assertTrue(initStrategy.validateConfig(validConfig1));

        String validConfig2 = segStrategy.getDefaultConfig();
        assertTrue(segStrategy.validateConfig(validConfig2));

        // 测试无效配置
        String invalidConfig = "{ invalid json }";
        assertFalse(initStrategy.validateConfig(invalidConfig));
        assertFalse(segStrategy.validateConfig(invalidConfig));

        // 测试空配置（应该使用默认配置，因此有效）
        assertTrue(initStrategy.validateConfig(null));
        assertTrue(initStrategy.validateConfig(""));

        System.out.println("策略配置验证测试通过");
    }

    @Test
    public void testStrategyMetadata() {
        DefaultInitializationStrategy initStrategy = new DefaultInitializationStrategy();
        DefaultRecursiveSegmentationStrategy segStrategy = new DefaultRecursiveSegmentationStrategy();

        // 测试策略元数据
        assertNotNull(initStrategy.getStrategyType());
        assertNotNull(initStrategy.getStrategyName());
        assertNotNull(initStrategy.getStrategyDescription());
        assertNotNull(initStrategy.getDefaultConfig());

        assertNotNull(segStrategy.getStrategyType());
        assertNotNull(segStrategy.getStrategyName());
        assertNotNull(segStrategy.getStrategyDescription());
        assertNotNull(segStrategy.getDefaultConfig());

        // 测试执行优先级
        assertTrue(initStrategy.getExecutionPriority() < segStrategy.getExecutionPriority());

        // 测试并行执行支持
        assertFalse(initStrategy.supportParallelExecution()); // 初始化策略不支持并行
        assertTrue(segStrategy.supportParallelExecution()); // 分割策略支持并行

        System.out.println("策略元数据测试通过:");
        System.out.println("初始化策略: " + initStrategy.getStrategyName() + " (优先级: " + initStrategy.getExecutionPriority() + ")");
        System.out.println("分割策略: " + segStrategy.getStrategyName() + " (优先级: " + segStrategy.getExecutionPriority() + ")");
    }

    @Test
    public void testEmptyContentHandling() {
        // 测试空内容处理
        KnowledgeDocument document = new KnowledgeDocument();
        document.setId(1L);
        document.setName("空文档.txt");
        document.setFormat("txt");

        SegmentationContext context = new SegmentationContext(document, "", 1L);
        DefaultRecursiveSegmentationStrategy strategy = new DefaultRecursiveSegmentationStrategy();

        try {
            SegmentationResult result = strategy.execute(context, null);
            
            // 空内容应该返回失败结果
            assertNotNull(result);
            assertFalse(result.isSuccess());
            assertNotNull(result.getMessage());
            
            System.out.println("空内容处理测试通过: " + result.getMessage());
            
        } catch (Exception e) {
            fail("空内容处理测试失败: " + e.getMessage());
        }
    }
}
