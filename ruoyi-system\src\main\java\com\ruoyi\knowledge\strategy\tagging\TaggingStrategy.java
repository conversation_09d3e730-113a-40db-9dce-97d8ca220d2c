package com.ruoyi.knowledge.strategy.tagging;

import com.ruoyi.knowledge.strategy.KnowledgeStrategy;
import com.ruoyi.knowledge.enums.StrategyType;

/**
 * 标签策略接口
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
public interface TaggingStrategy extends KnowledgeStrategy<TaggingContext, TaggingResult> {
    
    @Override
    default StrategyType getStrategyType() {
        return StrategyType.TAGGING;
    }
    
    @Override
    default int getExecutionPriority() {
        return 30; // 标签策略在段落切分之后执行
    }
}
