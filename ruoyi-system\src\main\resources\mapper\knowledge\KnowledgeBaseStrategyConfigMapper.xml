<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.knowledge.mapper.KnowledgeBaseStrategyConfigMapper">
    
    <resultMap type="KnowledgeBaseStrategyConfig" id="KnowledgeBaseStrategyConfigResult">
        <result property="id"    column="id"    />
        <result property="knowledgeBaseId"    column="knowledge_base_id"    />
        <result property="strategyTemplateId"    column="strategy_template_id"    />
        <result property="strategyType"    column="strategy_type"    />
        <result property="configJson"    column="config_json"    />
        <result property="isEnabled"    column="is_enabled"    />
        <result property="executionOrder"    column="execution_order"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectKnowledgeBaseStrategyConfigVo">
        select id, knowledge_base_id, strategy_template_id, strategy_type, config_json, is_enabled, execution_order, create_by, create_time, update_by, update_time from knowledge_base_strategy_config
    </sql>

    <select id="selectKnowledgeBaseStrategyConfigList" parameterType="KnowledgeBaseStrategyConfig" resultMap="KnowledgeBaseStrategyConfigResult">
        <include refid="selectKnowledgeBaseStrategyConfigVo"/>
        <where>  
            <if test="knowledgeBaseId != null "> and knowledge_base_id = #{knowledgeBaseId}</if>
            <if test="strategyTemplateId != null "> and strategy_template_id = #{strategyTemplateId}</if>
            <if test="strategyType != null  and strategyType != ''"> and strategy_type = #{strategyType}</if>
            <if test="isEnabled != null  and isEnabled != ''"> and is_enabled = #{isEnabled}</if>
        </where>
        order by execution_order asc, create_time desc
    </select>
    
    <select id="selectKnowledgeBaseStrategyConfigById" parameterType="Long" resultMap="KnowledgeBaseStrategyConfigResult">
        <include refid="selectKnowledgeBaseStrategyConfigVo"/>
        where id = #{id}
    </select>

    <select id="selectKnowledgeBaseStrategyConfigByKnowledgeBaseId" parameterType="Long" resultMap="KnowledgeBaseStrategyConfigResult">
        <include refid="selectKnowledgeBaseStrategyConfigVo"/>
        where knowledge_base_id = #{knowledgeBaseId}
        order by execution_order asc, create_time desc
    </select>

    <select id="selectKnowledgeBaseStrategyConfigByKnowledgeBaseIdAndType" resultMap="KnowledgeBaseStrategyConfigResult">
        <include refid="selectKnowledgeBaseStrategyConfigVo"/>
        where knowledge_base_id = #{knowledgeBaseId} and strategy_type = #{strategyType}
        order by execution_order asc, create_time desc
    </select>

    <select id="selectEnabledKnowledgeBaseStrategyConfigs" parameterType="Long" resultMap="KnowledgeBaseStrategyConfigResult">
        <include refid="selectKnowledgeBaseStrategyConfigVo"/>
        where knowledge_base_id = #{knowledgeBaseId} and is_enabled = '1'
        order by execution_order asc, create_time desc
    </select>
        
    <insert id="insertKnowledgeBaseStrategyConfig" parameterType="KnowledgeBaseStrategyConfig" useGeneratedKeys="true" keyProperty="id">
        insert into knowledge_base_strategy_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="knowledgeBaseId != null">knowledge_base_id,</if>
            <if test="strategyTemplateId != null">strategy_template_id,</if>
            <if test="strategyType != null and strategyType != ''">strategy_type,</if>
            <if test="configJson != null">config_json,</if>
            <if test="isEnabled != null">is_enabled,</if>
            <if test="executionOrder != null">execution_order,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="knowledgeBaseId != null">#{knowledgeBaseId},</if>
            <if test="strategyTemplateId != null">#{strategyTemplateId},</if>
            <if test="strategyType != null and strategyType != ''">#{strategyType},</if>
            <if test="configJson != null">#{configJson},</if>
            <if test="isEnabled != null">#{isEnabled},</if>
            <if test="executionOrder != null">#{executionOrder},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateKnowledgeBaseStrategyConfig" parameterType="KnowledgeBaseStrategyConfig">
        update knowledge_base_strategy_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="knowledgeBaseId != null">knowledge_base_id = #{knowledgeBaseId},</if>
            <if test="strategyTemplateId != null">strategy_template_id = #{strategyTemplateId},</if>
            <if test="strategyType != null and strategyType != ''">strategy_type = #{strategyType},</if>
            <if test="configJson != null">config_json = #{configJson},</if>
            <if test="isEnabled != null">is_enabled = #{isEnabled},</if>
            <if test="executionOrder != null">execution_order = #{executionOrder},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteKnowledgeBaseStrategyConfigById" parameterType="Long">
        delete from knowledge_base_strategy_config where id = #{id}
    </delete>

    <delete id="deleteKnowledgeBaseStrategyConfigByIds" parameterType="String">
        delete from knowledge_base_strategy_config where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteKnowledgeBaseStrategyConfigByKnowledgeBaseId" parameterType="Long">
        delete from knowledge_base_strategy_config where knowledge_base_id = #{knowledgeBaseId}
    </delete>

</mapper>
