-- AI聊天模块数据库表结构

-- 创建AI聊天会话表
DROP TABLE IF EXISTS `ai_chat_session`;
CREATE TABLE `ai_chat_session` (
  `session_id` varchar(64) NOT NULL COMMENT '会话ID',
  `title` varchar(100) DEFAULT '新对话' COMMENT '会话标题',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `user_name` varchar(30) NOT NULL COMMENT '用户名',
  `model` varchar(50) DEFAULT 'qwen-plus' COMMENT 'AI模型',
  `status` char(1) DEFAULT '0' COMMENT '会话状态（0正常 1停用）',
  `last_active_time` datetime DEFAULT NULL COMMENT '最后活跃时间',
  `message_count` int(11) DEFAULT '0' COMMENT '消息数量',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`session_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_last_active_time` (`last_active_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI聊天会话表';

-- 创建AI聊天消息表
DROP TABLE IF EXISTS `ai_chat_message`;
CREATE TABLE `ai_chat_message` (
  `message_id` varchar(64) NOT NULL COMMENT '消息ID',
  `session_id` varchar(64) NOT NULL COMMENT '会话ID',
  `role` varchar(20) NOT NULL COMMENT '消息角色（user/assistant）',
  `content` longtext NOT NULL COMMENT '消息内容',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `user_name` varchar(30) DEFAULT NULL COMMENT '用户名',
  `model` varchar(50) DEFAULT NULL COMMENT 'AI模型',
  `status` char(1) DEFAULT '0' COMMENT '消息状态（0正常 1删除）',
  `response_time` bigint(20) DEFAULT NULL COMMENT '响应时间（毫秒）',
  `token_count` int(11) DEFAULT NULL COMMENT 'Token数量',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`message_id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_role` (`role`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI聊天消息表';

-- 插入菜单数据
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('智能问答', 0, 4, 'ai', NULL, 1, 0, 'M', '0', '0', NULL, 'search', 'admin', sysdate(), '', NULL, '智能问答菜单');

-- 获取刚插入的父菜单ID
SET @parent_menu_id = LAST_INSERT_ID();

-- 插入子菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES 
('智能问答', @parent_menu_id, 1, 'chat', 'ai/chat/index', 1, 0, 'C', '0', '0', 'ai:chat:view', 'message', 'admin', sysdate(), '', NULL, '智能问答页面'),
('会话管理', @parent_menu_id, 2, 'session', 'ai/session/index', 1, 0, 'C', '0', '0', 'ai:session:list', 'list', 'admin', sysdate(), '', NULL, '会话管理页面');

-- 获取智能问答菜单ID
SET @chat_menu_id = (SELECT menu_id FROM sys_menu WHERE path = 'chat' AND parent_id = @parent_menu_id);

-- 插入智能问答按钮权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES 
('发送消息', @chat_menu_id, 1, '', '', 1, 0, 'F', '0', '0', 'ai:chat:send', '#', 'admin', sysdate(), '', NULL, ''),
('查看历史', @chat_menu_id, 2, '', '', 1, 0, 'F', '0', '0', 'ai:chat:history', '#', 'admin', sysdate(), '', NULL, ''),
('创建会话', @chat_menu_id, 3, '', '', 1, 0, 'F', '0', '0', 'ai:chat:session', '#', 'admin', sysdate(), '', NULL, ''),
('导出记录', @chat_menu_id, 4, '', '', 1, 0, 'F', '0', '0', 'ai:chat:export', '#', 'admin', sysdate(), '', NULL, '');

-- 获取会话管理菜单ID
SET @session_menu_id = (SELECT menu_id FROM sys_menu WHERE path = 'session' AND parent_id = @parent_menu_id);

-- 插入会话管理按钮权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES 
('会话查询', @session_menu_id, 1, '', '', 1, 0, 'F', '0', '0', 'ai:session:query', '#', 'admin', sysdate(), '', NULL, ''),
('会话新增', @session_menu_id, 2, '', '', 1, 0, 'F', '0', '0', 'ai:session:add', '#', 'admin', sysdate(), '', NULL, ''),
('会话修改', @session_menu_id, 3, '', '', 1, 0, 'F', '0', '0', 'ai:session:edit', '#', 'admin', sysdate(), '', NULL, ''),
('会话删除', @session_menu_id, 4, '', '', 1, 0, 'F', '0', '0', 'ai:session:remove', '#', 'admin', sysdate(), '', NULL, ''),
('会话导出', @session_menu_id, 5, '', '', 1, 0, 'F', '0', '0', 'ai:session:export', '#', 'admin', sysdate(), '', NULL, '');

-- 为管理员角色分配AI相关权限
INSERT INTO sys_role_menu (role_id, menu_id) 
SELECT 1, menu_id FROM sys_menu WHERE perms LIKE 'ai:%' OR (parent_id IN (SELECT menu_id FROM sys_menu WHERE path = 'ai'));

COMMIT;
