package com.ruoyi.ai.service;

import java.util.List;
import com.ruoyi.ai.domain.AiChatMessage;

/**
 * AI聊天消息Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-09
 */
public interface IAiChatMessageService 
{
    /**
     * 查询AI聊天消息
     * 
     * @param messageId AI聊天消息主键
     * @return AI聊天消息
     */
    public AiChatMessage selectAiChatMessageByMessageId(String messageId);

    /**
     * 查询AI聊天消息列表
     * 
     * @param aiChatMessage AI聊天消息
     * @return AI聊天消息集合
     */
    public List<AiChatMessage> selectAiChatMessageList(AiChatMessage aiChatMessage);

    /**
     * 根据会话ID查询消息列表
     * 
     * @param sessionId 会话ID
     * @return AI聊天消息集合
     */
    public List<AiChatMessage> selectAiChatMessageListBySessionId(String sessionId);

    /**
     * 根据会话ID查询最近的消息列表（用于上下文）
     * 
     * @param sessionId 会话ID
     * @param limit 限制数量
     * @return AI聊天消息集合
     */
    public List<AiChatMessage> selectRecentMessagesBySessionId(String sessionId, int limit);

    /**
     * 新增AI聊天消息
     * 
     * @param aiChatMessage AI聊天消息
     * @return 结果
     */
    public int insertAiChatMessage(AiChatMessage aiChatMessage);

    /**
     * 修改AI聊天消息
     * 
     * @param aiChatMessage AI聊天消息
     * @return 结果
     */
    public int updateAiChatMessage(AiChatMessage aiChatMessage);

    /**
     * 批量删除AI聊天消息
     * 
     * @param messageIds 需要删除的AI聊天消息主键集合
     * @return 结果
     */
    public int deleteAiChatMessageByMessageIds(String[] messageIds);

    /**
     * 删除AI聊天消息信息
     * 
     * @param messageId AI聊天消息主键
     * @return 结果
     */
    public int deleteAiChatMessageByMessageId(String messageId);

    /**
     * 根据会话ID删除消息
     * 
     * @param sessionId 会话ID
     * @return 结果
     */
    public int deleteAiChatMessageBySessionId(String sessionId);

    /**
     * 根据会话ID清空消息（软删除）
     * 
     * @param sessionId 会话ID
     * @return 结果
     */
    public int clearAiChatMessageBySessionId(String sessionId);

    /**
     * 保存用户消息
     * 
     * @param sessionId 会话ID
     * @param content 消息内容
     * @param userId 用户ID
     * @param userName 用户名
     * @param model AI模型
     * @return 消息ID
     */
    public String saveUserMessage(String sessionId, String content, Long userId, String userName, String model);

    /**
     * 保存AI消息
     * 
     * @param sessionId 会话ID
     * @param content 消息内容
     * @param model AI模型
     * @param responseTime 响应时间
     * @param tokenCount Token数量
     * @return 消息ID
     */
    public String saveAiMessage(String sessionId, String content, String model, Long responseTime, Long tokenCount);

    /**
     * 统计会话消息数量
     * 
     * @param sessionId 会话ID
     * @return 消息数量
     */
    public int countMessagesBySessionId(String sessionId);
}
