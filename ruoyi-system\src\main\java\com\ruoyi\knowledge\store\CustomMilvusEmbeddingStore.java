package com.ruoyi.knowledge.store;

import com.ruoyi.knowledge.config.MilvusConfig;
import dev.langchain4j.data.embedding.Embedding;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.store.embedding.EmbeddingMatch;
import dev.langchain4j.store.embedding.EmbeddingSearchRequest;
import dev.langchain4j.store.embedding.EmbeddingSearchResult;
import dev.langchain4j.store.embedding.EmbeddingStore;
import dev.langchain4j.store.embedding.milvus.MilvusEmbeddingStore;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 自定义Milvus向量存储实现
 * 支持多知识库的集合管理
 * 
 * <AUTHOR>
 * @date 2024-08-13
 */
@Component
public class CustomMilvusEmbeddingStore implements EmbeddingStore<TextSegment> {

    private static final Logger logger = LoggerFactory.getLogger(CustomMilvusEmbeddingStore.class);

    @Autowired(required = false)
    private MilvusConfig milvusConfig;

    /** 缓存不同知识库对应的MilvusEmbeddingStore实例 */
    private final Map<Long, MilvusEmbeddingStore> storeCache = new ConcurrentHashMap<>();

    /** 默认的MilvusEmbeddingStore实例（用于全局搜索） */
    private MilvusEmbeddingStore defaultStore;

    @PostConstruct
    public void init() {
        if (milvusConfig == null) {
            logger.warn("Milvus配置未找到，CustomMilvusEmbeddingStore将不可用");
            return;
        }

        // 验证配置
        if (!validateConfig()) {
            logger.error("Milvus配置验证失败，CustomMilvusEmbeddingStore将不可用");
            return;
        }

        logger.info("初始化CustomMilvusEmbeddingStore，Milvus地址: {}", milvusConfig.getUri());
        logger.info("Milvus配置详情: host={}, port={}, database={}, secure={}",
                milvusConfig.getHost(), milvusConfig.getPort(), milvusConfig.getDatabase(), milvusConfig.isSecure());

        // 创建默认的存储实例
        try {
            defaultStore = createMilvusStore("default_collection");
            logger.info("默认Milvus存储实例创建成功");

            // 测试连接
            testConnection();
        } catch (Exception e) {
            logger.error("创建默认Milvus存储实例失败，Milvus功能将不可用: {}", e.getMessage(), e);
            defaultStore = null;
        }
    }

    /**
     * 验证Milvus配置
     */
    private boolean validateConfig() {
        if (milvusConfig.getHost() == null || milvusConfig.getHost().trim().isEmpty()) {
            logger.error("Milvus host配置为空");
            return false;
        }

        if (milvusConfig.getPort() <= 0 || milvusConfig.getPort() > 65535) {
            logger.error("Milvus port配置无效: {}", milvusConfig.getPort());
            return false;
        }

        String uri = milvusConfig.getUri();
        if (uri == null || uri.trim().isEmpty()) {
            logger.error("生成的Milvus URI为空");
            return false;
        }

        logger.info("Milvus配置验证通过: {}", uri);
        return true;
    }

    /**
     * 测试Milvus连接
     */
    private void testConnection() {
        try {
            if (defaultStore != null) {
                // 尝试执行一个简单的搜索来测试连接
                logger.info("测试Milvus连接...");
                // 这里可以添加具体的连接测试逻辑
                logger.info("Milvus连接测试成功");
            }
        } catch (Exception e) {
            logger.warn("Milvus连接测试失败: {}", e.getMessage());
        }
    }

    /**
     * 获取指定知识库的MilvusEmbeddingStore实例
     */
    public MilvusEmbeddingStore getStoreForKnowledgeBase(Long knowledgeBaseId) {
        return storeCache.computeIfAbsent(knowledgeBaseId, id -> {
            try {
                String collectionName = milvusConfig.getCollectionName(id);
                logger.info("为知识库 {} 创建Milvus存储实例，集合名称: {}", id, collectionName);
                return createMilvusStore(collectionName);
            } catch (Exception e) {
                logger.error("为知识库 {} 创建Milvus存储实例失败: {}", id, e.getMessage(), e);
                return defaultStore; // 失败时返回默认实例
            }
        });
    }

    /**
     * 创建MilvusEmbeddingStore实例
     */
    private MilvusEmbeddingStore createMilvusStore(String collectionName) {
        logger.info("开始创建MilvusEmbeddingStore实例，集合名称: {}", collectionName);
        logger.info("Milvus连接参数: URI={}, 维度={}", milvusConfig.getUri(), milvusConfig.getDefaultDimension());

        try {
            MilvusEmbeddingStore.Builder builder = MilvusEmbeddingStore.builder()
                    .uri(milvusConfig.getUri())
                    .collectionName(collectionName)
                    .dimension(milvusConfig.getDefaultDimension())
                    .autoFlushOnInsert(true);

            // 如果需要认证
            if (milvusConfig.needsAuth()) {
                logger.info("使用认证连接，用户名: {}", milvusConfig.getUsername());
                builder.username(milvusConfig.getUsername())
                       .password(milvusConfig.getPassword());
            } else {
                logger.info("使用无认证连接");
            }

            // 如果指定了数据库
            if (milvusConfig.getDatabase() != null && !milvusConfig.getDatabase().equals("default")) {
                logger.info("使用数据库: {}", milvusConfig.getDatabase());
                builder.databaseName(milvusConfig.getDatabase());
            } else {
                logger.info("使用默认数据库");
            }

            MilvusEmbeddingStore store = builder.build();
            logger.info("MilvusEmbeddingStore实例创建成功");
            return store;

        } catch (Exception e) {
            logger.error("创建MilvusEmbeddingStore实例时发生错误: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public String add(Embedding embedding) {
        if (defaultStore == null) {
            throw new RuntimeException("默认Milvus存储实例未初始化");
        }
        return defaultStore.add(embedding);
    }

    @Override
    public void add(String id, Embedding embedding) {
        if (defaultStore == null) {
            throw new RuntimeException("默认Milvus存储实例未初始化");
        }
        defaultStore.add(id, embedding);
    }

    @Override
    public String add(Embedding embedding, TextSegment textSegment) {
        // 从元数据中获取知识库ID
        String knowledgeBaseIdStr = textSegment.metadata().getString("knowledgeBaseId");
        if (knowledgeBaseIdStr != null) {
            try {
                Long knowledgeBaseId = Long.parseLong(knowledgeBaseIdStr);
                MilvusEmbeddingStore store = getStoreForKnowledgeBase(knowledgeBaseId);
                return store.add(embedding, textSegment);
            } catch (NumberFormatException e) {
                logger.warn("无效的知识库ID: {}, 使用默认存储", knowledgeBaseIdStr);
                if (defaultStore != null) {
                    return defaultStore.add(embedding, textSegment);
                }
            }
        } else {
            logger.warn("文本段落缺少知识库ID元数据，使用默认存储");
            if (defaultStore != null) {
                return defaultStore.add(embedding, textSegment);
            }
        }
        return null; // 如果所有存储都不可用
    }



    @Override
    public List<String> addAll(List<Embedding> embeddings) {
        if (defaultStore == null) {
            throw new RuntimeException("默认Milvus存储实例未初始化");
        }
        return defaultStore.addAll(embeddings);
    }

    @Override
    public List<String> addAll(List<Embedding> embeddings, List<TextSegment> textSegments) {
        if (embeddings.size() != textSegments.size()) {
            throw new IllegalArgumentException("嵌入向量和文本段落数量不匹配");
        }

        List<String> allIds = new java.util.ArrayList<>();

        // 按知识库ID分组
        Map<Long, List<Integer>> groupByKnowledgeBase = new ConcurrentHashMap<>();

        for (int i = 0; i < textSegments.size(); i++) {
            TextSegment segment = textSegments.get(i);
            String knowledgeBaseIdStr = segment.metadata().getString("knowledgeBaseId");

            if (knowledgeBaseIdStr != null) {
                try {
                    Long knowledgeBaseId = Long.parseLong(knowledgeBaseIdStr);
                    groupByKnowledgeBase.computeIfAbsent(knowledgeBaseId, k -> new java.util.ArrayList<>()).add(i);
                } catch (NumberFormatException e) {
                    logger.warn("无效的知识库ID: {}", knowledgeBaseIdStr);
                }
            }
        }

        // 分别向不同的知识库存储
        for (Map.Entry<Long, List<Integer>> entry : groupByKnowledgeBase.entrySet()) {
            Long knowledgeBaseId = entry.getKey();
            List<Integer> indices = entry.getValue();

            List<Embedding> batchEmbeddings = new java.util.ArrayList<>();
            List<TextSegment> batchSegments = new java.util.ArrayList<>();

            for (Integer index : indices) {
                batchEmbeddings.add(embeddings.get(index));
                batchSegments.add(textSegments.get(index));
            }

            MilvusEmbeddingStore store = getStoreForKnowledgeBase(knowledgeBaseId);
            List<String> batchIds = store.addAll(batchEmbeddings, batchSegments);
            if (batchIds != null) {
                allIds.addAll(batchIds);
            }

            logger.info("向知识库 {} 批量添加 {} 个向量", knowledgeBaseId, batchEmbeddings.size());
        }

        return allIds;
    }

    @Override
    public EmbeddingSearchResult<TextSegment> search(EmbeddingSearchRequest embeddingSearchRequest) {
        if (defaultStore == null) {
            throw new RuntimeException("默认Milvus存储实例未初始化");
        }
        return defaultStore.search(embeddingSearchRequest);
    }

    /**
     * 在指定知识库中搜索
     */
    public EmbeddingSearchResult<TextSegment> searchInKnowledgeBase(Long knowledgeBaseId, EmbeddingSearchRequest embeddingSearchRequest) {
        try {
            logger.info("开始在知识库 {} 中搜索，请求参数: maxResults={}, minScore={}",
                    knowledgeBaseId, embeddingSearchRequest.maxResults(), embeddingSearchRequest.minScore());

            MilvusEmbeddingStore store = getStoreForKnowledgeBase(knowledgeBaseId);
            if (store == null) {
                logger.error("无法获取知识库 {} 的Milvus存储实例", knowledgeBaseId);
                return new EmbeddingSearchResult<>(new ArrayList<>());
            }

            String collectionName = milvusConfig.getCollectionName(knowledgeBaseId);
            logger.info("在集合 {} 中搜索", collectionName);

            EmbeddingSearchResult<TextSegment> result = store.search(embeddingSearchRequest);
            logger.info("知识库 {} 搜索完成，找到 {} 个结果", knowledgeBaseId, result.matches().size());

            return result;
        } catch (Exception e) {
            logger.error("在知识库 {} 中搜索时发生错误: {}", knowledgeBaseId, e.getMessage(), e);
            return new EmbeddingSearchResult<>(new ArrayList<>());
        }
    }

    /**
     * 删除指定知识库的集合
     */
    public void dropKnowledgeBaseCollection(Long knowledgeBaseId) {
        try {
            MilvusEmbeddingStore store = storeCache.remove(knowledgeBaseId);
            if (store != null) {
                // 注意：LangChain4j的MilvusEmbeddingStore可能没有直接的删除集合方法
                // 这里我们只是从缓存中移除，实际的集合删除需要通过Milvus客户端进行
                logger.info("从缓存中移除知识库 {} 的存储实例", knowledgeBaseId);
            }
        } catch (Exception e) {
            logger.error("删除知识库 {} 的集合失败: {}", knowledgeBaseId, e.getMessage(), e);
        }
    }

    /**
     * 获取缓存的存储实例数量
     */
    public int getCachedStoreCount() {
        return storeCache.size();
    }

    /**
     * 清理缓存
     */
    public void clearCache() {
        storeCache.clear();
        logger.info("清理Milvus存储实例缓存");
    }
}
