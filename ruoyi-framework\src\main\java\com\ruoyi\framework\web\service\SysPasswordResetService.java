package com.ruoyi.framework.web.service;

import java.util.concurrent.TimeUnit;
import java.util.UUID;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.SmsCodeBody;
import com.ruoyi.common.core.domain.model.VerifyPhoneBody;
import com.ruoyi.common.core.domain.model.ResetPasswordBody;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.service.ISysUserService;

/**
 * 密码重置服务
 * 
 * <AUTHOR>
 */
@Component
public class SysPasswordResetService {
    @Autowired
    private RedisCache redisCache;

    @Autowired
    private ISysUserService userService;

    /**
     * 发送短信验证码
     */
    public String sendSmsCode(SmsCodeBody smsCodeBody) {
        String msg = "";
        String username = smsCodeBody.getUsername();
        String phonenumber = smsCodeBody.getPhonenumber();

        if (StringUtils.isEmpty(username)) {
            msg = "用户名不能为空";
        } else if (StringUtils.isEmpty(phonenumber)) {
            msg = "手机号码不能为空";
        } else if (!isValidPhoneNumber(phonenumber)) {
            msg = "手机号码格式不正确";
        } else {
            // 验证用户名和手机号是否匹配
            SysUser user = userService.selectUserByUserName(username);
            if (StringUtils.isNull(user)) {
                msg = "用户不存在";
            } else if (!phonenumber.equals(user.getPhonenumber())) {
                msg = "手机号码与用户不匹配";
            } else {
                // 生成6位数字验证码
                String smsCode = generateSmsCode();

                // 将验证码存储到Redis，有效期5分钟
                String cacheKey = CacheConstants.SMS_CODE_KEY + username + ":" + phonenumber;
                redisCache.setCacheObject(cacheKey, smsCode, 5, TimeUnit.MINUTES);

                // 添加调试信息
                System.out.println("=== 发送验证码调试信息 ===");
                System.out.println("缓存键: " + cacheKey);
                System.out.println("生成的验证码: " + smsCode);
                System.out.println("发送短信验证码到 " + phonenumber + "，验证码：" + smsCode);
                System.out.println("========================");

                // 实际项目中，这里应该调用短信服务商的API
                // smsService.sendSms(phonenumber, smsCode);
            }
        }
        return msg;
    }

    /**
     * 验证手机号和短信验证码
     */
    public AjaxResult verifyPhone(VerifyPhoneBody verifyPhoneBody) {
        String username = verifyPhoneBody.getUsername();
        String phonenumber = verifyPhoneBody.getPhonenumber();
        String smsCode = verifyPhoneBody.getSmsCode();

        if (StringUtils.isEmpty(username)) {
            return AjaxResult.error("用户名不能为空");
        }
        if (StringUtils.isEmpty(phonenumber)) {
            return AjaxResult.error("手机号码不能为空");
        }
        if (StringUtils.isEmpty(smsCode)) {
            return AjaxResult.error("验证码不能为空");
        }

        // 从Redis获取验证码
        String cacheKey = CacheConstants.SMS_CODE_KEY + username + ":" + phonenumber;
        String cachedSmsCode = redisCache.getCacheObject(cacheKey);

        // 添加调试信息
        System.out.println("=== 验证阶段调试信息 ===");
        System.out.println("缓存键: " + cacheKey);
        System.out.println("输入的验证码: " + smsCode);
        System.out.println("缓存中的验证码: " + cachedSmsCode);
        System.out.println("========================");

        if (StringUtils.isEmpty(cachedSmsCode)) {
            System.out.println("错误：验证码已过期或不存在");
            return AjaxResult.error("验证码已过期，请重新获取");
        }

        if (!smsCode.equals(cachedSmsCode)) {
            System.out.println("错误：验证码不匹配");
            return AjaxResult.error("验证码错误");
        }

        // 验证成功，删除验证码缓存
        redisCache.deleteObject(cacheKey);

        // 生成重置令牌
        String resetToken = UUID.randomUUID().toString();
        String resetTokenKey = CacheConstants.PWD_RESET_TOKEN_KEY + resetToken;

        // 将重置令牌存储到Redis，有效期10分钟
        redisCache.setCacheObject(resetTokenKey, username, 10, TimeUnit.MINUTES);

        return AjaxResult.success().put("resetToken", resetToken);
    }

    /**
     * 重置密码
     */
    public String resetPassword(ResetPasswordBody resetPasswordBody) {
        String msg = "";
        String resetToken = resetPasswordBody.getResetToken();
        String newPassword = resetPasswordBody.getNewPassword();
        String confirmPassword = resetPasswordBody.getConfirmPassword();

        if (StringUtils.isEmpty(resetToken)) {
            msg = "重置令牌不能为空";
        } else if (StringUtils.isEmpty(newPassword)) {
            msg = "新密码不能为空";
        } else if (StringUtils.isEmpty(confirmPassword)) {
            msg = "确认密码不能为空";
        } else if (!newPassword.equals(confirmPassword)) {
            msg = "两次输入的密码不一致";
        } else if (newPassword.length() < UserConstants.PASSWORD_MIN_LENGTH
                || newPassword.length() > UserConstants.PASSWORD_MAX_LENGTH) {
            msg = "密码长度必须在5到20个字符之间";
        } else {
            // 验证重置令牌
            String resetTokenKey = CacheConstants.PWD_RESET_TOKEN_KEY + resetToken;
            String username = redisCache.getCacheObject(resetTokenKey);

            if (StringUtils.isEmpty(username)) {
                msg = "重置令牌无效或已过期";
            } else {
                // 获取用户信息
                SysUser user = userService.selectUserByUserName(username);
                if (StringUtils.isNull(user)) {
                    msg = "用户不存在";
                } else {
                    // 更新密码
                    user.setPassword(SecurityUtils.encryptPassword(newPassword));
                    user.setPwdUpdateDate(DateUtils.getNowDate());
                    user.setUpdateBy("system");
                    user.setUpdateTime(DateUtils.getNowDate());

                    int result = userService.resetUserPwd(user.getUserId(), user.getPassword());
                    if (result > 0) {
                        // 删除重置令牌
                        redisCache.deleteObject(resetTokenKey);
                    } else {
                        msg = "密码重置失败";
                    }
                }
            }
        }
        return msg;
    }

    /**
     * 验证手机号码格式
     */
    private boolean isValidPhoneNumber(String phonenumber) {
        if (StringUtils.isEmpty(phonenumber)) {
            return false;
        }
        // 中国大陆手机号码正则表达式
        String regex = "^1[3-9]\\d{9}$";
        return phonenumber.matches(regex);
    }

    /**
     * 生成6位数字验证码
     */
    private String generateSmsCode() {
        return String.valueOf((int) ((Math.random() * 9 + 1) * 100000));
    }
}
