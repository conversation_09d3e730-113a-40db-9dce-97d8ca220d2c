package com.ruoyi.ai.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.ai.domain.AiChatSession;

/**
 * AI聊天会话Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-09
 */
public interface AiChatSessionMapper {
    /**
     * 查询AI聊天会话
     * 
     * @param sessionId AI聊天会话主键
     * @return AI聊天会话
     */
    public AiChatSession selectAiChatSessionBySessionId(String sessionId);

    /**
     * 查询AI聊天会话列表
     * 
     * @param aiChatSession AI聊天会话
     * @return AI聊天会话集合
     */
    public List<AiChatSession> selectAiChatSessionList(AiChatSession aiChatSession);

    /**
     * 根据用户ID查询会话列表
     * 
     * @param userId 用户ID
     * @return AI聊天会话集合
     */
    public List<AiChatSession> selectAiChatSessionListByUserId(Long userId);

    /**
     * 新增AI聊天会话
     * 
     * @param aiChatSession AI聊天会话
     * @return 结果
     */
    public int insertAiChatSession(AiChatSession aiChatSession);

    /**
     * 修改AI聊天会话
     * 
     * @param aiChatSession AI聊天会话
     * @return 结果
     */
    public int updateAiChatSession(AiChatSession aiChatSession);

    /**
     * 删除AI聊天会话
     * 
     * @param sessionId AI聊天会话主键
     * @return 结果
     */
    public int deleteAiChatSessionBySessionId(String sessionId);

    /**
     * 批量删除AI聊天会话
     * 
     * @param sessionIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAiChatSessionBySessionIds(String[] sessionIds);

    /**
     * 更新会话最后活跃时间
     * 
     * @param sessionId 会话ID
     * @return 结果
     */
    public int updateLastActiveTime(String sessionId);

    /**
     * 增加会话消息数量
     * 
     * @param sessionId 会话ID
     * @param count     增加数量
     * @return 结果
     */
    public int increaseMessageCount(@Param("sessionId") String sessionId, @Param("count") int count);

    /**
     * 检查会话是否属于用户
     * 
     * @param sessionId 会话ID
     * @param userId    用户ID
     * @return 结果
     */
    public int checkSessionOwnership(@Param("sessionId") String sessionId, @Param("userId") Long userId);
}
