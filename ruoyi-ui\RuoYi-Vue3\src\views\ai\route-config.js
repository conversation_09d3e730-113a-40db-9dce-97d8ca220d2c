// AI智能问答模块路由配置示例
// 注意：若依框架通常通过后端动态加载菜单路由，此文件仅作为参考

import Layout from '@/layout'

// 如果需要手动添加到路由中，可以参考以下配置
export const aiRoutes = {
  path: '/ai',
  component: Layout,
  redirect: '/ai/chat',
  name: 'AI',
  meta: {
    title: '智能问答',
    icon: 'search'
  },
  children: [
    {
      path: 'chat',
      component: () => import('@/views/ai/chat/index'),
      name: 'AiChat',
      meta: {
        title: '智能问答',
        icon: 'message',
        keepAlive: true
      }
    }
  ]
}

// 动态路由配置（如果需要权限控制）
export const aiDynamicRoutes = [
  {
    path: '/ai',
    component: Layout,
    redirect: '/ai/chat',
    name: 'AI',
    meta: {
      title: '智能问答',
      icon: 'search'
    },
    permissions: ['ai:chat:view'], // 权限控制
    children: [
      {
        path: 'chat',
        component: () => import('@/views/ai/chat/index'),
        name: 'AiChat',
        meta: {
          title: '智能问答',
          icon: 'message',
          keepAlive: true
        }
      }
    ]
  }
]

// 如果需要添加到现有路由中，可以在 router/index.js 中导入并添加：
/*
import { aiRoutes } from '@/views/ai/route-config'

// 添加到 constantRoutes 或 dynamicRoutes 中
export const constantRoutes = [
  // ... 其他路由
  aiRoutes
]
*/
