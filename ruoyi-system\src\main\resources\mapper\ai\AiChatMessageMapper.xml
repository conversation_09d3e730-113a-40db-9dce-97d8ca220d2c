<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ai.mapper.AiChatMessageMapper">
    
    <resultMap type="AiChatMessage" id="AiChatMessageResult">
        <result property="messageId"    column="message_id"    />
        <result property="sessionId"    column="session_id"    />
        <result property="role"    column="role"    />
        <result property="content"    column="content"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="model"    column="model"    />
        <result property="status"    column="status"    />
        <result property="responseTime"    column="response_time"    />
        <result property="tokenCount"    column="token_count"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectAiChatMessageVo">
        select message_id, session_id, role, content, user_id, user_name, model, status, response_time, token_count, create_by, create_time, update_by, update_time, remark from ai_chat_message
    </sql>

    <select id="selectAiChatMessageList" parameterType="AiChatMessage" resultMap="AiChatMessageResult">
        <include refid="selectAiChatMessageVo"/>
        <where>  
            <if test="sessionId != null  and sessionId != ''"> and session_id = #{sessionId}</if>
            <if test="role != null  and role != ''"> and role = #{role}</if>
            <if test="content != null  and content != ''"> and content like concat('%', #{content}, '%')</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="model != null  and model != ''"> and model = #{model}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by create_time asc
    </select>
    
    <select id="selectAiChatMessageByMessageId" parameterType="String" resultMap="AiChatMessageResult">
        <include refid="selectAiChatMessageVo"/>
        where message_id = #{messageId}
    </select>

    <select id="selectAiChatMessageListBySessionId" parameterType="String" resultMap="AiChatMessageResult">
        <include refid="selectAiChatMessageVo"/>
        where session_id = #{sessionId} and status = '0'
        order by create_time asc
    </select>

    <select id="selectRecentMessagesBySessionId" resultMap="AiChatMessageResult">
        <include refid="selectAiChatMessageVo"/>
        where session_id = #{sessionId} and status = '0'
        order by create_time desc
        limit #{limit}
    </select>
        
    <insert id="insertAiChatMessage" parameterType="AiChatMessage">
        insert into ai_chat_message
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="messageId != null">message_id,</if>
            <if test="sessionId != null">session_id,</if>
            <if test="role != null">role,</if>
            <if test="content != null">content,</if>
            <if test="userId != null">user_id,</if>
            <if test="userName != null">user_name,</if>
            <if test="model != null">model,</if>
            <if test="status != null">status,</if>
            <if test="responseTime != null">response_time,</if>
            <if test="tokenCount != null">token_count,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="messageId != null">#{messageId},</if>
            <if test="sessionId != null">#{sessionId},</if>
            <if test="role != null">#{role},</if>
            <if test="content != null">#{content},</if>
            <if test="userId != null">#{userId},</if>
            <if test="userName != null">#{userName},</if>
            <if test="model != null">#{model},</if>
            <if test="status != null">#{status},</if>
            <if test="responseTime != null">#{responseTime},</if>
            <if test="tokenCount != null">#{tokenCount},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateAiChatMessage" parameterType="AiChatMessage">
        update ai_chat_message
        <trim prefix="SET" suffixOverrides=",">
            <if test="sessionId != null">session_id = #{sessionId},</if>
            <if test="role != null">role = #{role},</if>
            <if test="content != null">content = #{content},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="model != null">model = #{model},</if>
            <if test="status != null">status = #{status},</if>
            <if test="responseTime != null">response_time = #{responseTime},</if>
            <if test="tokenCount != null">token_count = #{tokenCount},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where message_id = #{messageId}
    </update>

    <delete id="deleteAiChatMessageByMessageId" parameterType="String">
        delete from ai_chat_message where message_id = #{messageId}
    </delete>

    <delete id="deleteAiChatMessageByMessageIds" parameterType="String">
        delete from ai_chat_message where message_id in 
        <foreach item="messageId" collection="array" open="(" separator="," close=")">
            #{messageId}
        </foreach>
    </delete>

    <delete id="deleteAiChatMessageBySessionId" parameterType="String">
        delete from ai_chat_message where session_id = #{sessionId}
    </delete>

    <update id="clearAiChatMessageBySessionId" parameterType="String">
        update ai_chat_message set status = '1' where session_id = #{sessionId}
    </update>

    <select id="countMessagesBySessionId" parameterType="String" resultType="int">
        select count(1) from ai_chat_message where session_id = #{sessionId} and status = '0'
    </select>

</mapper>
