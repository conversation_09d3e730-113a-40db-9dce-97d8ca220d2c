package com.ruoyi.knowledge.strategy.tagging;

import java.util.ArrayList;
import java.util.List;

/**
 * 标签策略执行结果
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
public class TaggingResult {
    
    /** 是否成功 */
    private boolean success;
    
    /** 错误消息 */
    private String message;
    
    /** 生成的标签列表 */
    private List<Tag> tags;
    
    /** 执行耗时（毫秒） */
    private long executionTime;
    
    public TaggingResult() {
        this.tags = new ArrayList<>();
    }
    
    public TaggingResult(boolean success, String message) {
        this.success = success;
        this.message = message;
        this.tags = new ArrayList<>();
    }
    
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public List<Tag> getTags() {
        return tags;
    }
    
    public void setTags(List<Tag> tags) {
        this.tags = tags;
    }
    
    public void addTag(Tag tag) {
        if (this.tags == null) {
            this.tags = new ArrayList<>();
        }
        this.tags.add(tag);
    }
    
    public void addTag(String name, double score, String type) {
        addTag(new Tag(name, score, type));
    }
    
    public long getExecutionTime() {
        return executionTime;
    }
    
    public void setExecutionTime(long executionTime) {
        this.executionTime = executionTime;
    }
    
    /**
     * 获取标签名称列表
     */
    public List<String> getTagNames() {
        List<String> names = new ArrayList<>();
        if (tags != null) {
            for (Tag tag : tags) {
                names.add(tag.getName());
            }
        }
        return names;
    }
    
    /**
     * 获取标签字符串（逗号分隔）
     */
    public String getTagsAsString() {
        return String.join(",", getTagNames());
    }
    
    @Override
    public String toString() {
        return "TaggingResult{" +
                "success=" + success +
                ", message='" + message + '\'' +
                ", tagsCount=" + (tags != null ? tags.size() : 0) +
                ", executionTime=" + executionTime +
                '}';
    }
    
    /**
     * 标签类
     */
    public static class Tag {
        /** 标签名称 */
        private String name;
        
        /** 标签权重/置信度 */
        private double score;
        
        /** 标签类型 */
        private String type;
        
        public Tag() {}
        
        public Tag(String name, double score, String type) {
            this.name = name;
            this.score = score;
            this.type = type;
        }
        
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
        
        public double getScore() {
            return score;
        }
        
        public void setScore(double score) {
            this.score = score;
        }
        
        public String getType() {
            return type;
        }
        
        public void setType(String type) {
            this.type = type;
        }
        
        @Override
        public String toString() {
            return "Tag{" +
                    "name='" + name + '\'' +
                    ", score=" + score +
                    ", type='" + type + '\'' +
                    '}';
        }
    }
}
