package com.ruoyi.knowledge.service.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.knowledge.domain.KnowledgeStrategyTemplate;
import com.ruoyi.knowledge.mapper.KnowledgeStrategyTemplateMapper;
import com.ruoyi.knowledge.service.IKnowledgeStrategyTemplateService;
import com.ruoyi.knowledge.strategy.StrategyFactory;
import com.ruoyi.knowledge.strategy.KnowledgeStrategy;
import com.ruoyi.knowledge.enums.StrategyType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 知识库策略模板Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
@Service
public class KnowledgeStrategyTemplateServiceImpl implements IKnowledgeStrategyTemplateService 
{
    @Autowired
    private KnowledgeStrategyTemplateMapper knowledgeStrategyTemplateMapper;
    
    @Autowired
    private StrategyFactory strategyFactory;

    /**
     * 查询知识库策略模板
     * 
     * @param id 知识库策略模板主键
     * @return 知识库策略模板
     */
    @Override
    public KnowledgeStrategyTemplate selectKnowledgeStrategyTemplateById(Long id)
    {
        return knowledgeStrategyTemplateMapper.selectKnowledgeStrategyTemplateById(id);
    }

    /**
     * 查询知识库策略模板列表
     * 
     * @param knowledgeStrategyTemplate 知识库策略模板
     * @return 知识库策略模板
     */
    @Override
    public List<KnowledgeStrategyTemplate> selectKnowledgeStrategyTemplateList(KnowledgeStrategyTemplate knowledgeStrategyTemplate)
    {
        return knowledgeStrategyTemplateMapper.selectKnowledgeStrategyTemplateList(knowledgeStrategyTemplate);
    }

    /**
     * 新增知识库策略模板
     * 
     * @param knowledgeStrategyTemplate 知识库策略模板
     * @return 结果
     */
    @Override
    public int insertKnowledgeStrategyTemplate(KnowledgeStrategyTemplate knowledgeStrategyTemplate)
    {
        knowledgeStrategyTemplate.setCreateTime(DateUtils.getNowDate());
        knowledgeStrategyTemplate.setCreateBy(SecurityUtils.getUsername());
        knowledgeStrategyTemplate.setCreatorId(SecurityUtils.getUserId());
        knowledgeStrategyTemplate.setCreatorName(SecurityUtils.getUsername());
        return knowledgeStrategyTemplateMapper.insertKnowledgeStrategyTemplate(knowledgeStrategyTemplate);
    }

    /**
     * 修改知识库策略模板
     * 
     * @param knowledgeStrategyTemplate 知识库策略模板
     * @return 结果
     */
    @Override
    public int updateKnowledgeStrategyTemplate(KnowledgeStrategyTemplate knowledgeStrategyTemplate)
    {
        knowledgeStrategyTemplate.setUpdateTime(DateUtils.getNowDate());
        knowledgeStrategyTemplate.setUpdateBy(SecurityUtils.getUsername());
        return knowledgeStrategyTemplateMapper.updateKnowledgeStrategyTemplate(knowledgeStrategyTemplate);
    }

    /**
     * 批量删除知识库策略模板
     * 
     * @param ids 需要删除的知识库策略模板主键
     * @return 结果
     */
    @Override
    public int deleteKnowledgeStrategyTemplateByIds(Long[] ids)
    {
        return knowledgeStrategyTemplateMapper.deleteKnowledgeStrategyTemplateByIds(ids);
    }

    /**
     * 删除知识库策略模板信息
     * 
     * @param id 知识库策略模板主键
     * @return 结果
     */
    @Override
    public int deleteKnowledgeStrategyTemplateById(Long id)
    {
        return knowledgeStrategyTemplateMapper.deleteKnowledgeStrategyTemplateById(id);
    }

    @Override
    public List<KnowledgeStrategyTemplate> selectKnowledgeStrategyTemplateByType(String strategyType) {
        return knowledgeStrategyTemplateMapper.selectKnowledgeStrategyTemplateByType(strategyType);
    }

    @Override
    public KnowledgeStrategyTemplate selectDefaultKnowledgeStrategyTemplateByType(String strategyType) {
        return knowledgeStrategyTemplateMapper.selectDefaultKnowledgeStrategyTemplateByType(strategyType);
    }

    @Override
    public List<KnowledgeStrategyTemplate> selectEnabledKnowledgeStrategyTemplates() {
        return knowledgeStrategyTemplateMapper.selectEnabledKnowledgeStrategyTemplates();
    }

    @Override
    public boolean validateStrategyConfig(String strategyType, String configJson) {
        try {
            StrategyType type = StrategyType.fromCode(strategyType);
            KnowledgeStrategy<?, ?> strategy = strategyFactory.getDefaultStrategy(type);
            if (strategy != null) {
                return strategy.validateConfig(configJson);
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }
}
