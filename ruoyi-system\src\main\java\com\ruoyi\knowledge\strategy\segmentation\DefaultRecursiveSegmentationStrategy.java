package com.ruoyi.knowledge.strategy.segmentation;

import com.fasterxml.jackson.databind.JsonNode;
import com.ruoyi.knowledge.strategy.AbstractKnowledgeStrategy;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 默认递归段落切分策略实现
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
@Component
public class DefaultRecursiveSegmentationStrategy extends AbstractKnowledgeStrategy<SegmentationContext, SegmentationResult> 
        implements SegmentationStrategy {
    
    @Override
    public String getStrategyName() {
        return "递归文档分割策略";
    }
    
    @Override
    public String getStrategyDescription() {
        return "使用递归方式分割文档，适合大多数文档类型";
    }
    
    @Override
    public String getDefaultConfig() {
        return "{"
                + "\"maxChunkSize\": 500,"
                + "\"chunkOverlap\": 50,"
                + "\"separators\": [\"\\n\\n\", \"\\n\", \" \", \"\"]"
                + "}";
    }
    
    @Override
    protected SegmentationResult doExecute(SegmentationContext input, String config) throws Exception {
        JsonNode configNode = parseConfig(config);
        
        int maxChunkSize = getConfigInt(configNode, "maxChunkSize", 500);
        int chunkOverlap = getConfigInt(configNode, "chunkOverlap", 50);
        
        logger.info("开始执行递归段落切分策略，文档: {}, 内容长度: {}", 
                input.getDocument().getName(), 
                input.getContent() != null ? input.getContent().length() : 0);
        
        SegmentationResult result = new SegmentationResult();
        
        try {
            if (input.getContent() == null || input.getContent().trim().isEmpty()) {
                result.setSuccess(false);
                result.setMessage("文档内容为空");
                return result;
            }
            
            // 执行递归分割
            List<String> chunks = recursiveSplit(input.getContent(), maxChunkSize, chunkOverlap);
            
            // 转换为TextSegment
            List<SegmentationResult.TextSegment> segments = new ArrayList<>();
            for (int i = 0; i < chunks.size(); i++) {
                SegmentationResult.TextSegment segment = new SegmentationResult.TextSegment(chunks.get(i), i);
                segment.addMetadata("documentId", input.getDocument().getId());
                segment.addMetadata("documentName", input.getDocument().getName());
                segment.addMetadata("knowledgeBaseId", input.getKnowledgeBaseId());
                segments.add(segment);
            }
            
            result.setSuccess(true);
            result.setMessage("段落切分成功");
            result.setSegments(segments);
            result.setAttribute("totalSegments", segments.size());
            result.setAttribute("averageSegmentLength", chunks.stream().mapToInt(String::length).average().orElse(0));
            
            logger.info("递归段落切分策略执行成功，生成段落数量: {}", segments.size());
            
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage("段落切分失败: " + e.getMessage());
            logger.error("递归段落切分策略执行失败", e);
            throw e;
        }
        
        return result;
    }
    
    /**
     * 递归分割文本
     */
    private List<String> recursiveSplit(String text, int maxChunkSize, int chunkOverlap) {
        List<String> separators = Arrays.asList("\n\n", "\n", " ", "");
        return recursiveSplitWithSeparators(text, maxChunkSize, chunkOverlap, separators, 0);
    }
    
    private List<String> recursiveSplitWithSeparators(String text, int maxChunkSize, int chunkOverlap, 
                                                     List<String> separators, int separatorIndex) {
        List<String> result = new ArrayList<>();
        
        if (text.length() <= maxChunkSize) {
            result.add(text);
            return result;
        }
        
        if (separatorIndex >= separators.size()) {
            // 没有更多分隔符，强制按长度分割
            for (int i = 0; i < text.length(); i += maxChunkSize - chunkOverlap) {
                int end = Math.min(i + maxChunkSize, text.length());
                result.add(text.substring(i, end));
            }
            return result;
        }
        
        String separator = separators.get(separatorIndex);
        String[] parts = text.split(separator, -1);
        
        if (parts.length == 1) {
            // 当前分隔符无效，尝试下一个
            return recursiveSplitWithSeparators(text, maxChunkSize, chunkOverlap, separators, separatorIndex + 1);
        }
        
        StringBuilder currentChunk = new StringBuilder();
        for (String part : parts) {
            if (currentChunk.length() + part.length() + separator.length() <= maxChunkSize) {
                if (currentChunk.length() > 0) {
                    currentChunk.append(separator);
                }
                currentChunk.append(part);
            } else {
                if (currentChunk.length() > 0) {
                    result.add(currentChunk.toString());
                    currentChunk = new StringBuilder();
                }
                
                if (part.length() > maxChunkSize) {
                    // 递归处理过长的部分
                    result.addAll(recursiveSplitWithSeparators(part, maxChunkSize, chunkOverlap, separators, separatorIndex + 1));
                } else {
                    currentChunk.append(part);
                }
            }
        }
        
        if (currentChunk.length() > 0) {
            result.add(currentChunk.toString());
        }
        
        return result;
    }
}
