package com.ruoyi.knowledge.strategy.segmentation;

import com.ruoyi.knowledge.domain.KnowledgeDocument;

/**
 * 段落切分策略上下文
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
public class SegmentationContext {
    
    /** 文档信息 */
    private KnowledgeDocument document;
    
    /** 文档内容 */
    private String content;
    
    /** 知识库ID */
    private Long knowledgeBaseId;
    
    public SegmentationContext() {
    }
    
    public SegmentationContext(KnowledgeDocument document, String content, Long knowledgeBaseId) {
        this.document = document;
        this.content = content;
        this.knowledgeBaseId = knowledgeBaseId;
    }
    
    public KnowledgeDocument getDocument() {
        return document;
    }
    
    public void setDocument(KnowledgeDocument document) {
        this.document = document;
    }
    
    public String getContent() {
        return content;
    }
    
    public void setContent(String content) {
        this.content = content;
    }
    
    public Long getKnowledgeBaseId() {
        return knowledgeBaseId;
    }
    
    public void setKnowledgeBaseId(Long knowledgeBaseId) {
        this.knowledgeBaseId = knowledgeBaseId;
    }
    
    @Override
    public String toString() {
        return "SegmentationContext{" +
                "document=" + document +
                ", contentLength=" + (content != null ? content.length() : 0) +
                ", knowledgeBaseId=" + knowledgeBaseId +
                '}';
    }
}
