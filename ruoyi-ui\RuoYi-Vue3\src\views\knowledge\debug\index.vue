<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>知识库调试工具</span>
        </div>
      </template>
      
      <!-- 知识库选择 -->
      <el-form :model="debugForm" label-width="120px">
        <el-form-item label="选择知识库">
          <el-select v-model="debugForm.knowledgeBaseId" placeholder="请选择知识库" style="width: 300px;" @change="handleKnowledgeBaseChange">
            <el-option 
              v-for="kb in knowledgeBaseList" 
              :key="kb.id" 
              :label="`${kb.name} (ID: ${kb.id})`" 
              :value="kb.id"
            />
          </el-select>
          <el-button type="primary" @click="loadKnowledgeBaseList" style="margin-left: 10px;">刷新列表</el-button>
        </el-form-item>
      </el-form>

      <el-divider />

      <!-- 调试信息显示 -->
      <div v-if="debugInfo">
        <h3>知识库信息</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="知识库存在">
            <el-tag :type="debugInfo.knowledgeBaseExists ? 'success' : 'danger'">
              {{ debugInfo.knowledgeBaseExists ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="知识库名称">{{ debugInfo.knowledgeBaseName || '未知' }}</el-descriptions-item>
          <el-descriptions-item label="知识库状态">
            <el-tag :type="debugInfo.knowledgeBaseStatus === '0' ? 'success' : 'warning'">
              {{ debugInfo.knowledgeBaseStatus === '0' ? '正常' : '停用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="记录的文档数量">{{ debugInfo.documentCount || 0 }}</el-descriptions-item>
          <el-descriptions-item label="实际文档数量">{{ debugInfo.actualDocumentCount || 0 }}</el-descriptions-item>
          <el-descriptions-item label="测试搜索结果数">{{ debugInfo.testSearchResultCount || 0 }}</el-descriptions-item>
        </el-descriptions>

        <el-divider />

        <!-- 文档列表 -->
        <h3>文档列表</h3>
        <el-table :data="debugInfo.documents" style="width: 100%" max-height="300">
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="name" label="文档名称" />
          <el-table-column prop="format" label="格式" width="100" />
          <el-table-column prop="size" label="大小" width="100" />
          <el-table-column prop="processStatus" label="处理状态" width="120">
            <template #default="scope">
              <el-tag :type="scope.row.processStatus === '1' ? 'success' : 'warning'">
                {{ scope.row.processStatus === '1' ? '已处理' : '未处理' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>

        <el-divider />

        <!-- 测试搜索结果 -->
        <h3>测试搜索结果 (查询: "{{ debugInfo.testSearchQuery }}")</h3>
        <div v-if="debugInfo.testSearchResults && debugInfo.testSearchResults.length > 0">
          <el-card v-for="(result, index) in debugInfo.testSearchResults" :key="index" style="margin-bottom: 10px;">
            <div style="font-size: 12px; color: #666;">结果 {{ index + 1 }}:</div>
            <div>{{ result }}</div>
          </el-card>
        </div>
        <div v-else>
          <el-empty description="没有找到搜索结果" />
        </div>
      </div>

      <el-divider />

      <!-- 自定义搜索测试 -->
      <h3>自定义搜索测试</h3>
      <el-form :model="searchForm" label-width="120px">
        <el-form-item label="搜索内容">
          <el-input v-model="searchForm.query" placeholder="请输入搜索内容" style="width: 400px;" />
          <el-button type="primary" @click="testSearch" :loading="searchLoading" style="margin-left: 10px;">
            测试搜索
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 搜索结果 -->
      <div v-if="searchResults">
        <h4>搜索结果 ({{ searchResults.resultCount }} 条)</h4>
        <div v-if="searchResults.results && searchResults.results.length > 0">
          <el-card v-for="(result, index) in searchResults.results" :key="index" style="margin-bottom: 10px;">
            <div style="font-size: 12px; color: #666;">结果 {{ index + 1 }}:</div>
            <div>{{ result }}</div>
          </el-card>
        </div>
        <div v-else>
          <el-empty description="没有找到搜索结果" />
        </div>
      </div>

      <el-divider />

      <!-- 操作按钮 -->
      <div style="text-align: center;">
        <el-button type="primary" @click="getDebugInfo" :loading="loading" :disabled="!debugForm.knowledgeBaseId">
          获取调试信息
        </el-button>
        <el-button type="warning" @click="rebuildKnowledgeBase" :loading="rebuildLoading" :disabled="!debugForm.knowledgeBaseId">
          重建知识库索引
        </el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup name="KnowledgeDebug">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { listKnowledgeBase } from '@/api/knowledge/build'
import request from '@/utils/request'

// 数据
const loading = ref(false)
const searchLoading = ref(false)
const rebuildLoading = ref(false)
const knowledgeBaseList = ref([])
const debugInfo = ref(null)
const searchResults = ref(null)

const debugForm = ref({
  knowledgeBaseId: null
})

const searchForm = ref({
  query: ''
})

// 方法
const loadKnowledgeBaseList = async () => {
  try {
    const response = await listKnowledgeBase({})
    knowledgeBaseList.value = response.rows || []
    ElMessage.success('知识库列表加载成功')
  } catch (error) {
    ElMessage.error('加载知识库列表失败')
  }
}

const handleKnowledgeBaseChange = (knowledgeBaseId) => {
  debugInfo.value = null
  searchResults.value = null
  if (knowledgeBaseId) {
    getDebugInfo()
  }
}

const getDebugInfo = async () => {
  if (!debugForm.value.knowledgeBaseId) {
    ElMessage.warning('请先选择知识库')
    return
  }

  loading.value = true
  try {
    const response = await request({
      url: `/knowledge/debug/info/${debugForm.value.knowledgeBaseId}`,
      method: 'get'
    })
    debugInfo.value = response.data
    ElMessage.success('调试信息获取成功')
  } catch (error) {
    ElMessage.error('获取调试信息失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const testSearch = async () => {
  if (!debugForm.value.knowledgeBaseId) {
    ElMessage.warning('请先选择知识库')
    return
  }
  if (!searchForm.value.query.trim()) {
    ElMessage.warning('请输入搜索内容')
    return
  }

  searchLoading.value = true
  try {
    const response = await request({
      url: `/knowledge/debug/search/${debugForm.value.knowledgeBaseId}`,
      method: 'post',
      data: { query: searchForm.value.query }
    })
    searchResults.value = response.data
    ElMessage.success('搜索测试完成')
  } catch (error) {
    ElMessage.error('搜索测试失败: ' + error.message)
  } finally {
    searchLoading.value = false
  }
}

const rebuildKnowledgeBase = async () => {
  if (!debugForm.value.knowledgeBaseId) {
    ElMessage.warning('请先选择知识库')
    return
  }

  rebuildLoading.value = true
  try {
    const response = await request({
      url: `/knowledge/debug/rebuild/${debugForm.value.knowledgeBaseId}`,
      method: 'post'
    })
    ElMessage.success(`知识库重建完成，成功处理 ${response.data.successCount}/${response.data.totalDocuments} 个文档`)
    // 重新获取调试信息
    await getDebugInfo()
  } catch (error) {
    ElMessage.error('重建知识库失败: ' + error.message)
  } finally {
    rebuildLoading.value = false
  }
}

// 生命周期
onMounted(async () => {
  await loadKnowledgeBaseList()
})
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
