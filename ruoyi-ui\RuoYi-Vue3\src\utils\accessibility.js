/**
 * 无障碍访问工具函数
 * 修复Element Plus组件的无障碍访问问题
 */

/**
 * 修复aria-hidden警告
 * 确保设置了aria-hidden="true"的元素不会保持焦点
 */
export function fixAriaHiddenFocus() {
  // 监听DOM变化
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'attributes' && mutation.attributeName === 'aria-hidden') {
        const target = mutation.target
        if (target.getAttribute('aria-hidden') === 'true') {
          // 移除焦点
          if (target.contains(document.activeElement)) {
            document.activeElement.blur()
          }
          
          // 确保所有子元素也失去焦点
          const focusableElements = target.querySelectorAll(
            'a, button, input, textarea, select, details, [tabindex]:not([tabindex="-1"])'
          )
          focusableElements.forEach(el => {
            if (el === document.activeElement) {
              el.blur()
            }
            el.setAttribute('tabindex', '-1')
          })
        } else if (target.getAttribute('aria-hidden') === 'false' || !target.hasAttribute('aria-hidden')) {
          // 恢复焦点能力
          const focusableElements = target.querySelectorAll('[tabindex="-1"]')
          focusableElements.forEach(el => {
            el.removeAttribute('tabindex')
          })
        }
      }
    })
  })
  
  // 开始观察
  observer.observe(document.body, {
    attributes: true,
    attributeFilter: ['aria-hidden'],
    subtree: true
  })
  
  return observer
}

/**
 * 修复Element Plus菜单的焦点问题
 */
export function fixMenuFocus() {
  // 处理现有的aria-hidden元素
  const hiddenElements = document.querySelectorAll('[aria-hidden="true"]')
  hiddenElements.forEach(element => {
    if (element.contains(document.activeElement)) {
      document.activeElement.blur()
    }
  })
}

/**
 * 初始化无障碍访问修复
 */
export function initAccessibilityFix() {
  // DOM加载完成后执行
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      fixMenuFocus()
      fixAriaHiddenFocus()
    })
  } else {
    fixMenuFocus()
    fixAriaHiddenFocus()
  }
}

/**
 * 手动修复特定元素的无障碍问题
 * @param {Element} element 要修复的元素
 */
export function fixElementAccessibility(element) {
  if (!element) return
  
  // 如果元素被隐藏，确保其不保持焦点
  if (element.getAttribute('aria-hidden') === 'true') {
    if (element.contains(document.activeElement)) {
      document.activeElement.blur()
    }
    
    // 设置所有可聚焦子元素为不可聚焦
    const focusableElements = element.querySelectorAll(
      'a, button, input, textarea, select, details, [tabindex]:not([tabindex="-1"])'
    )
    focusableElements.forEach(el => {
      el.setAttribute('data-original-tabindex', el.getAttribute('tabindex') || '0')
      el.setAttribute('tabindex', '-1')
    })
  }
}

/**
 * 恢复元素的无障碍访问能力
 * @param {Element} element 要恢复的元素
 */
export function restoreElementAccessibility(element) {
  if (!element) return
  
  // 恢复所有子元素的焦点能力
  const elements = element.querySelectorAll('[data-original-tabindex]')
  elements.forEach(el => {
    const originalTabindex = el.getAttribute('data-original-tabindex')
    if (originalTabindex === '0') {
      el.removeAttribute('tabindex')
    } else {
      el.setAttribute('tabindex', originalTabindex)
    }
    el.removeAttribute('data-original-tabindex')
  })
}
