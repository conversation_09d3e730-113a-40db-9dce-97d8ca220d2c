package com.ruoyi.knowledge.strategy.segmentation;

import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 段落切分策略结果
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
public class SegmentationResult {
    
    /** 是否成功 */
    private boolean success;
    
    /** 结果消息 */
    private String message;
    
    /** 切分后的段落列表 */
    private List<TextSegment> segments;
    
    /** 扩展属性 */
    private Map<String, Object> attributes;
    
    public SegmentationResult() {
        this.attributes = new HashMap<>();
    }
    
    public SegmentationResult(boolean success, String message) {
        this();
        this.success = success;
        this.message = message;
    }
    
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public List<TextSegment> getSegments() {
        return segments;
    }
    
    public void setSegments(List<TextSegment> segments) {
        this.segments = segments;
    }
    
    public Map<String, Object> getAttributes() {
        return attributes;
    }
    
    public void setAttributes(Map<String, Object> attributes) {
        this.attributes = attributes;
    }
    
    public void setAttribute(String key, Object value) {
        this.attributes.put(key, value);
    }
    
    public Object getAttribute(String key) {
        return this.attributes.get(key);
    }
    
    /**
     * 文本段落类
     */
    public static class TextSegment {
        /** 段落内容 */
        private String text;
        
        /** 段落索引 */
        private int index;
        
        /** 段落元数据 */
        private Map<String, Object> metadata;
        
        public TextSegment() {
            this.metadata = new HashMap<>();
        }
        
        public TextSegment(String text, int index) {
            this();
            this.text = text;
            this.index = index;
        }
        
        public String getText() {
            return text;
        }
        
        public void setText(String text) {
            this.text = text;
        }
        
        public int getIndex() {
            return index;
        }
        
        public void setIndex(int index) {
            this.index = index;
        }
        
        public Map<String, Object> getMetadata() {
            return metadata;
        }
        
        public void setMetadata(Map<String, Object> metadata) {
            this.metadata = metadata;
        }
        
        public void addMetadata(String key, Object value) {
            this.metadata.put(key, value);
        }
        
        @Override
        public String toString() {
            return "TextSegment{" +
                    "text='" + (text != null ? text.substring(0, Math.min(50, text.length())) + "..." : "null") + '\'' +
                    ", index=" + index +
                    ", metadata=" + metadata +
                    '}';
        }
    }
    
    @Override
    public String toString() {
        return "SegmentationResult{" +
                "success=" + success +
                ", message='" + message + '\'' +
                ", segmentCount=" + (segments != null ? segments.size() : 0) +
                ", attributes=" + attributes +
                '}';
    }
}
