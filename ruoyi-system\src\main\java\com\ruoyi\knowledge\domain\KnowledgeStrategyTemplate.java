package com.ruoyi.knowledge.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * 知识库策略模板对象 knowledge_strategy_template
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
public class KnowledgeStrategyTemplate extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 策略模板ID */
    private Long id;

    /** 策略模板名称 */
    @Excel(name = "策略模板名称")
    private String name;

    /** 策略模板描述 */
    @Excel(name = "策略模板描述")
    private String description;

    /** 策略类型 */
    @Excel(name = "策略类型", readConverterExp = "INITIALIZATION=初始化策略,SEGMENTATION=段落切分策略,TAGGING=标签策略,SUMMARIZATION=归纳总结策略,ASSOCIATION=关联策略")
    private String strategyType;

    /** 策略配置JSON */
    private String configJson;

    /** 是否默认策略 */
    @Excel(name = "是否默认策略", readConverterExp = "0=否,1=是")
    private String isDefault;

    /** 是否启用 */
    @Excel(name = "是否启用", readConverterExp = "0=禁用,1=启用")
    private String isEnabled;

    /** 排序顺序 */
    @Excel(name = "排序顺序")
    private Integer sortOrder;

    /** 创建者ID */
    private Long creatorId;

    /** 创建者姓名 */
    @Excel(name = "创建者姓名")
    private String creatorName;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }

    public void setDescription(String description) 
    {
        this.description = description;
    }

    public String getDescription() 
    {
        return description;
    }

    public void setStrategyType(String strategyType) 
    {
        this.strategyType = strategyType;
    }

    public String getStrategyType() 
    {
        return strategyType;
    }

    public void setConfigJson(String configJson) 
    {
        this.configJson = configJson;
    }

    public String getConfigJson() 
    {
        return configJson;
    }

    public void setIsDefault(String isDefault) 
    {
        this.isDefault = isDefault;
    }

    public String getIsDefault() 
    {
        return isDefault;
    }

    public void setIsEnabled(String isEnabled) 
    {
        this.isEnabled = isEnabled;
    }

    public String getIsEnabled() 
    {
        return isEnabled;
    }

    public void setSortOrder(Integer sortOrder) 
    {
        this.sortOrder = sortOrder;
    }

    public Integer getSortOrder() 
    {
        return sortOrder;
    }

    public void setCreatorId(Long creatorId) 
    {
        this.creatorId = creatorId;
    }

    public Long getCreatorId() 
    {
        return creatorId;
    }

    public void setCreatorName(String creatorName) 
    {
        this.creatorName = creatorName;
    }

    public String getCreatorName() 
    {
        return creatorName;
    }

    @Override
    public String toString() {
        return "KnowledgeStrategyTemplate{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", strategyType='" + strategyType + '\'' +
                ", configJson='" + configJson + '\'' +
                ", isDefault='" + isDefault + '\'' +
                ", isEnabled='" + isEnabled + '\'' +
                ", sortOrder=" + sortOrder +
                ", creatorId=" + creatorId +
                ", creatorName='" + creatorName + '\'' +
                '}';
    }
}
