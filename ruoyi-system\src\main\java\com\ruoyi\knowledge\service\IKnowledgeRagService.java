package com.ruoyi.knowledge.service;

import com.ruoyi.knowledge.domain.KnowledgeDocument;
import java.util.List;

/**
 * 知识库RAG服务接口
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
public interface IKnowledgeRagService {

    /**
     * 创建知识库
     * 将选中的文档上传到向量数据库
     * 
     * @param knowledgeBaseName        知识库名称
     * @param knowledgeBaseDescription 知识库描述
     * @param documentIds              选中的文档ID列表
     * @return 创建结果
     */
    boolean createKnowledgeBase(String knowledgeBaseName, String knowledgeBaseDescription, List<Long> documentIds);

    /**
     * 创建知识库并返回知识库ID
     * 将选中的文档上传到向量数据库
     *
     * @param knowledgeBaseName        知识库名称
     * @param knowledgeBaseDescription 知识库描述
     * @param documentIds              选中的文档ID列表
     * @return 创建成功返回知识库ID，失败返回null
     */
    Long createKnowledgeBaseWithId(String knowledgeBaseName, String knowledgeBaseDescription, List<Long> documentIds);

    /**
     * 添加文档到知识库
     * 
     * @param knowledgeBaseId 知识库ID
     * @param document        文档对象
     * @return 添加结果
     */
    boolean addDocumentToKnowledgeBase(Long knowledgeBaseId, KnowledgeDocument document);

    /**
     * 从知识库中删除文档
     * 
     * @param knowledgeBaseId 知识库ID
     * @param documentId      文档ID
     * @return 删除结果
     */
    boolean removeDocumentFromKnowledgeBase(Long knowledgeBaseId, Long documentId);

    /**
     * 在知识库中搜索相关内容
     * 
     * @param knowledgeBaseId 知识库ID
     * @param query           查询内容
     * @param maxResults      最大结果数
     * @return 搜索结果
     */
    List<String> searchInKnowledgeBase(Long knowledgeBaseId, String query, int maxResults);

    /**
     * 检查知识库是否存在
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 是否存在
     */
    boolean knowledgeBaseExists(Long knowledgeBaseId);

    /**
     * 获取知识库中的文档数量
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 文档数量
     */
    long getDocumentCount(Long knowledgeBaseId);
}
