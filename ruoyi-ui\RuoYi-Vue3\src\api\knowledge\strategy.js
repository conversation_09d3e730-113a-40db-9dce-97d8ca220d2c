import request from '@/utils/request'

// 查询知识库策略模板列表
export function listStrategyTemplate(query) {
  return request({
    url: '/knowledge/strategy/template/list',
    method: 'get',
    params: query
  })
}

// 查询知识库策略模板详细
export function getStrategyTemplate(id) {
  return request({
    url: '/knowledge/strategy/template/' + id,
    method: 'get'
  })
}

// 新增知识库策略模板
export function addStrategyTemplate(data) {
  return request({
    url: '/knowledge/strategy/template',
    method: 'post',
    data: data
  })
}

// 修改知识库策略模板
export function updateStrategyTemplate(data) {
  return request({
    url: '/knowledge/strategy/template',
    method: 'put',
    data: data
  })
}

// 删除知识库策略模板
export function delStrategyTemplate(ids) {
  return request({
    url: '/knowledge/strategy/template/' + ids,
    method: 'delete'
  })
}

// 根据策略类型查询策略模板
export function getTemplatesByType(strategyType) {
  return request({
    url: '/knowledge/strategy/template/type/' + strategyType,
    method: 'get'
  })
}

// 获取所有启用的策略模板
export function getEnabledTemplates() {
  return request({
    url: '/knowledge/strategy/template/enabled',
    method: 'get'
  })
}

// 获取策略类型列表
export function getStrategyTypes() {
  return request({
    url: '/knowledge/strategy/types',
    method: 'get'
  })
}

// 验证策略配置
export function validateStrategyConfig(data) {
  return request({
    url: '/knowledge/strategy/validate',
    method: 'post',
    data: data
  })
}

// 导出知识库策略模板
export function exportStrategyTemplate(query) {
  return request({
    url: '/knowledge/strategy/template/export',
    method: 'post',
    params: query
  })
}
