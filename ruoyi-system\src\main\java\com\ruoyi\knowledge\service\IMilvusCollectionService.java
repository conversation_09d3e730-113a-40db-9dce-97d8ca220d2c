package com.ruoyi.knowledge.service;

/**
 * Milvus集合管理服务接口
 * 
 * <AUTHOR>
 * @date 2024-08-13
 */
public interface IMilvusCollectionService {

    /**
     * 创建知识库对应的集合
     * 
     * @param knowledgeBaseId 知识库ID
     * @param dimension 向量维度
     * @return 是否创建成功
     */
    boolean createCollection(Long knowledgeBaseId, int dimension);

    /**
     * 删除知识库对应的集合
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 是否删除成功
     */
    boolean dropCollection(Long knowledgeBaseId);

    /**
     * 检查集合是否存在
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 是否存在
     */
    boolean hasCollection(Long knowledgeBaseId);

    /**
     * 加载集合到内存
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 是否加载成功
     */
    boolean loadCollection(Long knowledgeBaseId);

    /**
     * 释放集合内存
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 是否释放成功
     */
    boolean releaseCollection(Long knowledgeBaseId);

    /**
     * 创建索引
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 是否创建成功
     */
    boolean createIndex(Long knowledgeBaseId);

    /**
     * 删除索引
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 是否删除成功
     */
    boolean dropIndex(Long knowledgeBaseId);

    /**
     * 获取集合统计信息
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 统计信息
     */
    CollectionStats getCollectionStats(Long knowledgeBaseId);

    /**
     * 刷新集合数据
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 是否刷新成功
     */
    boolean flushCollection(Long knowledgeBaseId);

    /**
     * 压缩集合
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 是否压缩成功
     */
    boolean compactCollection(Long knowledgeBaseId);

    /**
     * 集合统计信息类
     */
    class CollectionStats {
        private long entityCount;
        private long indexedEntityCount;
        private String status;
        private long memorySize;
        private long diskSize;

        public CollectionStats() {}

        public CollectionStats(long entityCount, long indexedEntityCount, String status, long memorySize, long diskSize) {
            this.entityCount = entityCount;
            this.indexedEntityCount = indexedEntityCount;
            this.status = status;
            this.memorySize = memorySize;
            this.diskSize = diskSize;
        }

        public long getEntityCount() {
            return entityCount;
        }

        public void setEntityCount(long entityCount) {
            this.entityCount = entityCount;
        }

        public long getIndexedEntityCount() {
            return indexedEntityCount;
        }

        public void setIndexedEntityCount(long indexedEntityCount) {
            this.indexedEntityCount = indexedEntityCount;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public long getMemorySize() {
            return memorySize;
        }

        public void setMemorySize(long memorySize) {
            this.memorySize = memorySize;
        }

        public long getDiskSize() {
            return diskSize;
        }

        public void setDiskSize(long diskSize) {
            this.diskSize = diskSize;
        }

        @Override
        public String toString() {
            return "CollectionStats{" +
                    "entityCount=" + entityCount +
                    ", indexedEntityCount=" + indexedEntityCount +
                    ", status='" + status + '\'' +
                    ", memorySize=" + memorySize +
                    ", diskSize=" + diskSize +
                    '}';
        }
    }
}
