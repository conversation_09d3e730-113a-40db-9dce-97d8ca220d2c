package com.ruoyi.knowledge.service;

import java.io.File;

/**
 * 图片OCR服务接口
 * 提供从图片中提取文本内容的功能
 * 
 * <AUTHOR>
 * @date 2024-07-15
 */
public interface IImageOcrService {

    /**
     * 从图片文件中提取文本内容
     * 
     * @param imageFile 图片文件
     * @return 提取的文本内容
     */
    String extractTextFromImage(File imageFile);
    
    /**
     * 从图片文件路径中提取文本内容
     * 
     * @param imagePath 图片文件路径
     * @return 提取的文本内容
     */
    String extractTextFromImage(String imagePath);
    
    /**
     * 检查OCR服务是否可用
     * 
     * @return 服务可用状态
     */
    boolean isOcrAvailable();
}
