package com.ruoyi.knowledge.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 知识库策略配置对象 knowledge_base_strategy_config
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
public class KnowledgeBaseStrategyConfig extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 配置ID */
    private Long id;

    /** 知识库ID */
    @Excel(name = "知识库ID")
    private Long knowledgeBaseId;

    /** 策略模板ID */
    @Excel(name = "策略模板ID")
    private Long strategyTemplateId;

    /** 策略类型 */
    @Excel(name = "策略类型", readConverterExp = "INITIALIZATION=初始化策略,SEGMENTATION=段落切分策略,TAGGING=标签策略,SUMMARIZATION=归纳总结策略,ASSOCIATION=关联策略")
    private String strategyType;

    /** 个性化配置JSON */
    private String configJson;

    /** 是否启用 */
    @Excel(name = "是否启用", readConverterExp = "0=禁用,1=启用")
    private String isEnabled;

    /** 执行顺序 */
    @Excel(name = "执行顺序")
    private Integer executionOrder;

    /** 策略模板信息（关联查询） */
    private KnowledgeStrategyTemplate strategyTemplate;

    /** 知识库信息（关联查询） */
    private KnowledgeBase knowledgeBase;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setKnowledgeBaseId(Long knowledgeBaseId) 
    {
        this.knowledgeBaseId = knowledgeBaseId;
    }

    public Long getKnowledgeBaseId() 
    {
        return knowledgeBaseId;
    }

    public void setStrategyTemplateId(Long strategyTemplateId) 
    {
        this.strategyTemplateId = strategyTemplateId;
    }

    public Long getStrategyTemplateId() 
    {
        return strategyTemplateId;
    }

    public void setStrategyType(String strategyType) 
    {
        this.strategyType = strategyType;
    }

    public String getStrategyType() 
    {
        return strategyType;
    }

    public void setConfigJson(String configJson) 
    {
        this.configJson = configJson;
    }

    public String getConfigJson() 
    {
        return configJson;
    }

    public void setIsEnabled(String isEnabled) 
    {
        this.isEnabled = isEnabled;
    }

    public String getIsEnabled() 
    {
        return isEnabled;
    }

    public void setExecutionOrder(Integer executionOrder) 
    {
        this.executionOrder = executionOrder;
    }

    public Integer getExecutionOrder() 
    {
        return executionOrder;
    }

    public KnowledgeStrategyTemplate getStrategyTemplate() {
        return strategyTemplate;
    }

    public void setStrategyTemplate(KnowledgeStrategyTemplate strategyTemplate) {
        this.strategyTemplate = strategyTemplate;
    }

    public KnowledgeBase getKnowledgeBase() {
        return knowledgeBase;
    }

    public void setKnowledgeBase(KnowledgeBase knowledgeBase) {
        this.knowledgeBase = knowledgeBase;
    }

    @Override
    public String toString() {
        return "KnowledgeBaseStrategyConfig{" +
                "id=" + id +
                ", knowledgeBaseId=" + knowledgeBaseId +
                ", strategyTemplateId=" + strategyTemplateId +
                ", strategyType='" + strategyType + '\'' +
                ", configJson='" + configJson + '\'' +
                ", isEnabled='" + isEnabled + '\'' +
                ", executionOrder=" + executionOrder +
                '}';
    }
}
