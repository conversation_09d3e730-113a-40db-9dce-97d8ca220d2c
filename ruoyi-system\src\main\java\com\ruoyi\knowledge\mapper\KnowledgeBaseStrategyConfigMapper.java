package com.ruoyi.knowledge.mapper;

import com.ruoyi.knowledge.domain.KnowledgeBaseStrategyConfig;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 知识库策略配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
public interface KnowledgeBaseStrategyConfigMapper 
{
    /**
     * 查询知识库策略配置
     * 
     * @param id 知识库策略配置主键
     * @return 知识库策略配置
     */
    public KnowledgeBaseStrategyConfig selectKnowledgeBaseStrategyConfigById(Long id);

    /**
     * 查询知识库策略配置列表
     * 
     * @param knowledgeBaseStrategyConfig 知识库策略配置
     * @return 知识库策略配置集合
     */
    public List<KnowledgeBaseStrategyConfig> selectKnowledgeBaseStrategyConfigList(KnowledgeBaseStrategyConfig knowledgeBaseStrategyConfig);

    /**
     * 新增知识库策略配置
     * 
     * @param knowledgeBaseStrategyConfig 知识库策略配置
     * @return 结果
     */
    public int insertKnowledgeBaseStrategyConfig(KnowledgeBaseStrategyConfig knowledgeBaseStrategyConfig);

    /**
     * 修改知识库策略配置
     * 
     * @param knowledgeBaseStrategyConfig 知识库策略配置
     * @return 结果
     */
    public int updateKnowledgeBaseStrategyConfig(KnowledgeBaseStrategyConfig knowledgeBaseStrategyConfig);

    /**
     * 删除知识库策略配置
     * 
     * @param id 知识库策略配置主键
     * @return 结果
     */
    public int deleteKnowledgeBaseStrategyConfigById(Long id);

    /**
     * 批量删除知识库策略配置
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteKnowledgeBaseStrategyConfigByIds(Long[] ids);

    /**
     * 根据知识库ID查询策略配置
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 策略配置列表
     */
    public List<KnowledgeBaseStrategyConfig> selectKnowledgeBaseStrategyConfigByKnowledgeBaseId(Long knowledgeBaseId);

    /**
     * 根据知识库ID和策略类型查询策略配置
     *
     * @param knowledgeBaseId 知识库ID
     * @param strategyType 策略类型
     * @return 策略配置列表
     */
    public List<KnowledgeBaseStrategyConfig> selectKnowledgeBaseStrategyConfigByKnowledgeBaseIdAndType(@Param("knowledgeBaseId") Long knowledgeBaseId, @Param("strategyType") String strategyType);

    /**
     * 根据知识库ID删除策略配置
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 结果
     */
    public int deleteKnowledgeBaseStrategyConfigByKnowledgeBaseId(Long knowledgeBaseId);

    /**
     * 查询启用的策略配置
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 启用的策略配置列表
     */
    public List<KnowledgeBaseStrategyConfig> selectEnabledKnowledgeBaseStrategyConfigs(Long knowledgeBaseId);
}
