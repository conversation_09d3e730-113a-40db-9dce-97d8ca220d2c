package com.ruoyi.knowledge.strategy.initialization;

import com.ruoyi.knowledge.domain.KnowledgeBase;
import com.ruoyi.knowledge.domain.KnowledgeDocument;

import java.util.List;

/**
 * 初始化策略上下文
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
public class InitializationContext {
    
    /** 知识库信息 */
    private KnowledgeBase knowledgeBase;
    
    /** 文档列表 */
    private List<KnowledgeDocument> documents;
    
    /** 创建者ID */
    private Long creatorId;
    
    /** 创建者名称 */
    private String creatorName;
    
    public InitializationContext() {
    }
    
    public InitializationContext(KnowledgeBase knowledgeBase, List<KnowledgeDocument> documents, Long creatorId, String creatorName) {
        this.knowledgeBase = knowledgeBase;
        this.documents = documents;
        this.creatorId = creatorId;
        this.creatorName = creatorName;
    }
    
    public KnowledgeBase getKnowledgeBase() {
        return knowledgeBase;
    }
    
    public void setKnowledgeBase(KnowledgeBase knowledgeBase) {
        this.knowledgeBase = knowledgeBase;
    }
    
    public List<KnowledgeDocument> getDocuments() {
        return documents;
    }
    
    public void setDocuments(List<KnowledgeDocument> documents) {
        this.documents = documents;
    }
    
    public Long getCreatorId() {
        return creatorId;
    }
    
    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }
    
    public String getCreatorName() {
        return creatorName;
    }
    
    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }
    
    @Override
    public String toString() {
        return "InitializationContext{" +
                "knowledgeBase=" + knowledgeBase +
                ", documents=" + (documents != null ? documents.size() + " documents" : "null") +
                ", creatorId=" + creatorId +
                ", creatorName='" + creatorName + '\'' +
                '}';
    }
}
