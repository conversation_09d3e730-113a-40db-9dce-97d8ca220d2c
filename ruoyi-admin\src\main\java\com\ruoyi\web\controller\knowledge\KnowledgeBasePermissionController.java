package com.ruoyi.web.controller.knowledge;

import java.util.ArrayList;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.knowledge.domain.KnowledgeBasePermission;
import com.ruoyi.knowledge.service.IKnowledgeBasePermissionService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.SecurityUtils;

/**
 * 知识库权限Controller
 * 
 * <AUTHOR>
 * @date 2025-08-22
 */
@RestController
@RequestMapping("/knowledge/permission")
public class KnowledgeBasePermissionController extends BaseController
{
    @Autowired
    private IKnowledgeBasePermissionService knowledgeBasePermissionService;

    /**
     * 查询知识库权限列表（只显示用户有管理权限的知识库）
     */
    @PreAuthorize("@ss.hasPermi('knowledge:permission:list')")
    @GetMapping("/list")
    public TableDataInfo list(KnowledgeBasePermission knowledgeBasePermission)
    {
        startPage();
        Long currentUserId = SecurityUtils.getUserId();

        // 查询当前用户有管理权限的知识库
        List<KnowledgeBasePermission> userPermissions = knowledgeBasePermissionService.selectKnowledgeBasePermissionByUserId(currentUserId);

        // 筛选出有管理权限的知识库ID
        List<Long> adminKnowledgeBaseIds = new ArrayList<>();
        for (KnowledgeBasePermission permission : userPermissions) {
            if ("admin".equals(permission.getPermissionType())) {
                adminKnowledgeBaseIds.add(permission.getKnowledgeBaseId());
            }
        }

        // 如果没有管理权限的知识库，返回空列表
        if (adminKnowledgeBaseIds.isEmpty()) {
            return getDataTable(new ArrayList<>());
        }

        // 查询这些知识库的所有权限信息
        List<KnowledgeBasePermission> allPermissions = new ArrayList<>();
        for (Long knowledgeBaseId : adminKnowledgeBaseIds) {
            List<KnowledgeBasePermission> kbPermissions = knowledgeBasePermissionService.selectKnowledgeBasePermissionByKnowledgeBaseId(knowledgeBaseId);
            allPermissions.addAll(kbPermissions);
        }

        // 如果指定了特定的知识库ID，进行过滤
        if (knowledgeBasePermission.getKnowledgeBaseId() != null) {
            allPermissions = allPermissions.stream()
                .filter(p -> p.getKnowledgeBaseId().equals(knowledgeBasePermission.getKnowledgeBaseId()))
                .collect(java.util.stream.Collectors.toList());
        }

        return getDataTable(allPermissions);
    }

    /**
     * 根据知识库ID查询权限列表（需要管理权限）
     */
    @PreAuthorize("@ss.hasPermi('knowledge:permission:query')")
    @GetMapping("/listByKnowledgeBase/{knowledgeBaseId}")
    public AjaxResult listByKnowledgeBase(@PathVariable("knowledgeBaseId") Long knowledgeBaseId)
    {
        Long currentUserId = SecurityUtils.getUserId();

        // 检查当前用户是否有该知识库的管理权限
        if (!knowledgeBasePermissionService.hasKnowledgeBasePermission(knowledgeBaseId, currentUserId, "admin")) {
            return error("您没有管理此知识库权限的权限");
        }

        List<KnowledgeBasePermission> list = knowledgeBasePermissionService.selectKnowledgeBasePermissionByKnowledgeBaseId(knowledgeBaseId);
        return success(list);
    }

    /**
     * 查询当前用户的知识库权限列表
     */
    @PreAuthorize("@ss.hasPermi('knowledge:my:list')")
    @GetMapping("/my")
    public AjaxResult myPermissions()
    {
        Long userId = SecurityUtils.getUserId();
        List<KnowledgeBasePermission> list = knowledgeBasePermissionService.selectKnowledgeBasePermissionByUserId(userId);
        return success(list);
    }

    /**
     * 检查用户对知识库的权限
     */
    @PreAuthorize("@ss.hasPermi('knowledge:permission:query')")
    @GetMapping("/check/{knowledgeBaseId}/{userId}")
    public AjaxResult checkPermission(@PathVariable("knowledgeBaseId") Long knowledgeBaseId, 
                                    @PathVariable("userId") Long userId)
    {
        KnowledgeBasePermission permission = knowledgeBasePermissionService.selectUserKnowledgeBasePermission(knowledgeBaseId, userId);
        return success(permission);
    }

    /**
     * 检查当前用户对知识库的权限
     */
    @GetMapping("/checkMy/{knowledgeBaseId}")
    public AjaxResult checkMyPermission(@PathVariable("knowledgeBaseId") Long knowledgeBaseId)
    {
        Long userId = SecurityUtils.getUserId();
        KnowledgeBasePermission permission = knowledgeBasePermissionService.selectUserKnowledgeBasePermission(knowledgeBaseId, userId);
        return success(permission);
    }

    /**
     * 检查用户是否有指定权限
     */
    @GetMapping("/hasPermission/{knowledgeBaseId}/{permissionType}")
    public AjaxResult hasPermission(@PathVariable("knowledgeBaseId") Long knowledgeBaseId, 
                                  @PathVariable("permissionType") String permissionType)
    {
        Long userId = SecurityUtils.getUserId();
        boolean hasPermission = knowledgeBasePermissionService.hasKnowledgeBasePermission(knowledgeBaseId, userId, permissionType);
        return success(hasPermission);
    }

    /**
     * 导出知识库权限列表
     */
    @PreAuthorize("@ss.hasPermi('knowledge:permission:export')")
    @Log(title = "知识库权限", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, KnowledgeBasePermission knowledgeBasePermission)
    {
        List<KnowledgeBasePermission> list = knowledgeBasePermissionService.selectKnowledgeBasePermissionList(knowledgeBasePermission);
        ExcelUtil<KnowledgeBasePermission> util = new ExcelUtil<KnowledgeBasePermission>(KnowledgeBasePermission.class);
        util.exportExcel(response, list, "知识库权限数据");
    }

    /**
     * 获取知识库权限详细信息
     */
    @PreAuthorize("@ss.hasPermi('knowledge:permission:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(knowledgeBasePermissionService.selectKnowledgeBasePermissionById(id));
    }

    /**
     * 新增知识库权限（需要管理权限）
     */
    @PreAuthorize("@ss.hasPermi('knowledge:permission:add')")
    @Log(title = "知识库权限", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KnowledgeBasePermission knowledgeBasePermission)
    {
        Long currentUserId = SecurityUtils.getUserId();

        // 检查当前用户是否有该知识库的管理权限
        if (!knowledgeBasePermissionService.hasKnowledgeBasePermission(knowledgeBasePermission.getKnowledgeBaseId(), currentUserId, "admin")) {
            return error("您没有管理此知识库权限的权限");
        }

        // 检查是否已存在权限
        KnowledgeBasePermission existingPermission = knowledgeBasePermissionService.selectUserKnowledgeBasePermission(
            knowledgeBasePermission.getKnowledgeBaseId(), knowledgeBasePermission.getUserId());

        if (existingPermission != null) {
            return error("该用户已拥有此知识库的权限，请使用修改功能更新权限类型");
        }

        return toAjax(knowledgeBasePermissionService.insertKnowledgeBasePermission(knowledgeBasePermission));
    }

    /**
     * 修改知识库权限（需要管理权限）
     */
    @PreAuthorize("@ss.hasPermi('knowledge:permission:edit')")
    @Log(title = "知识库权限", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KnowledgeBasePermission knowledgeBasePermission)
    {
        Long currentUserId = SecurityUtils.getUserId();

        // 检查当前用户是否有该知识库的管理权限
        if (!knowledgeBasePermissionService.hasKnowledgeBasePermission(knowledgeBasePermission.getKnowledgeBaseId(), currentUserId, "admin")) {
            return error("您没有管理此知识库权限的权限");
        }

        return toAjax(knowledgeBasePermissionService.updateKnowledgeBasePermission(knowledgeBasePermission));
    }

    /**
     * 删除知识库权限（需要管理权限）
     */
    @PreAuthorize("@ss.hasPermi('knowledge:permission:remove')")
    @Log(title = "知识库权限", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        Long currentUserId = SecurityUtils.getUserId();

        // 检查每个权限记录对应的知识库是否有管理权限
        for (Long id : ids) {
            KnowledgeBasePermission permission = knowledgeBasePermissionService.selectKnowledgeBasePermissionById(id);
            if (permission != null) {
                if (!knowledgeBasePermissionService.hasKnowledgeBasePermission(permission.getKnowledgeBaseId(), currentUserId, "admin")) {
                    return error("您没有管理知识库 ID:" + permission.getKnowledgeBaseId() + " 权限的权限");
                }
            }
        }

        return toAjax(knowledgeBasePermissionService.deleteKnowledgeBasePermissionByIds(ids));
    }

    /**
     * 批量授权（需要管理权限）
     */
    @PreAuthorize("@ss.hasPermi('knowledge:permission:add')")
    @Log(title = "批量授权", businessType = BusinessType.GRANT)
    @PostMapping("/batchGrant")
    public AjaxResult batchGrant(@RequestBody BatchGrantRequest request)
    {
        Long currentUserId = SecurityUtils.getUserId();
        String currentUserName = SecurityUtils.getUsername();

        // 检查当前用户是否有该知识库的管理权限
        if (!knowledgeBasePermissionService.hasKnowledgeBasePermission(request.getKnowledgeBaseId(), currentUserId, "admin")) {
            return error("您没有管理此知识库权限的权限");
        }

        int result = knowledgeBasePermissionService.batchGrantPermissions(
            request.getKnowledgeBaseId(),
            request.getUserIds(),
            request.getPermissionType(),
            currentUserId,
            currentUserName
        );

        return toAjax(result);
    }

    /**
     * 批量授权请求对象
     */
    public static class BatchGrantRequest {
        private Long knowledgeBaseId;
        private Long[] userIds;
        private String permissionType;

        public Long getKnowledgeBaseId() {
            return knowledgeBaseId;
        }

        public void setKnowledgeBaseId(Long knowledgeBaseId) {
            this.knowledgeBaseId = knowledgeBaseId;
        }

        public Long[] getUserIds() {
            return userIds;
        }

        public void setUserIds(Long[] userIds) {
            this.userIds = userIds;
        }

        public String getPermissionType() {
            return permissionType;
        }

        public void setPermissionType(String permissionType) {
            this.permissionType = permissionType;
        }
    }
}
