package com.ruoyi.ai.service;

import com.ruoyi.ai.domain.dto.AiChatRequest;
import com.ruoyi.ai.domain.dto.AiChatResponse;
import com.ruoyi.ai.domain.dto.AiSessionRequest;
import com.ruoyi.ai.domain.AiChatSession;
import com.ruoyi.ai.domain.AiChatMessage;

import java.util.List;

/**
 * AI聊天综合服务接口
 * 
 * <AUTHOR>
 * @date 2024-07-09
 */
public interface IAiChatService {
    /**
     * 创建新会话
     * 
     * @param request  会话创建请求
     * @param userId   用户ID
     * @param userName 用户名
     * @return 会话信息
     */
    AiChatSession createSession(AiSessionRequest request, Long userId, String userName);

    /**
     * 获取用户会话列表
     * 
     * @param userId 用户ID
     * @return 会话列表
     */
    List<AiChatSession> getUserSessions(Long userId);

    /**
     * 获取会话历史消息
     * 
     * @param sessionId 会话ID
     * @param userId    用户ID
     * @return 消息列表
     */
    List<AiChatMessage> getSessionHistory(String sessionId, Long userId);

    /**
     * 发送消息（同步）
     * 
     * @param request  聊天请求
     * @param userId   用户ID
     * @param userName 用户名
     * @return 聊天响应
     */
    AiChatResponse sendMessage(AiChatRequest request, Long userId, String userName);

    /**
     * 删除会话
     * 
     * @param sessionId 会话ID
     * @param userId    用户ID
     * @return 删除结果
     */
    boolean deleteSession(String sessionId, Long userId);

    /**
     * 清空会话消息
     * 
     * @param sessionId 会话ID
     * @param userId    用户ID
     * @return 清空结果
     */
    boolean clearSessionMessages(String sessionId, Long userId);

    /**
     * 获取AI模型列表
     * 
     * @return 模型列表
     */
    List<String> getAvailableModels();

    /**
     * 检查会话权限
     * 
     * @param sessionId 会话ID
     * @param userId    用户ID
     * @return 是否有权限
     */
    boolean checkSessionPermission(String sessionId, Long userId);
}
