package com.ruoyi.knowledge.service;

import com.ruoyi.knowledge.domain.KnowledgeBaseStrategyConfig;
import java.util.List;

/**
 * 知识库策略配置Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
public interface IKnowledgeBaseStrategyConfigService 
{
    /**
     * 查询知识库策略配置
     * 
     * @param id 知识库策略配置主键
     * @return 知识库策略配置
     */
    public KnowledgeBaseStrategyConfig selectKnowledgeBaseStrategyConfigById(Long id);

    /**
     * 查询知识库策略配置列表
     * 
     * @param knowledgeBaseStrategyConfig 知识库策略配置
     * @return 知识库策略配置集合
     */
    public List<KnowledgeBaseStrategyConfig> selectKnowledgeBaseStrategyConfigList(KnowledgeBaseStrategyConfig knowledgeBaseStrategyConfig);

    /**
     * 新增知识库策略配置
     * 
     * @param knowledgeBaseStrategyConfig 知识库策略配置
     * @return 结果
     */
    public int insertKnowledgeBaseStrategyConfig(KnowledgeBaseStrategyConfig knowledgeBaseStrategyConfig);

    /**
     * 修改知识库策略配置
     * 
     * @param knowledgeBaseStrategyConfig 知识库策略配置
     * @return 结果
     */
    public int updateKnowledgeBaseStrategyConfig(KnowledgeBaseStrategyConfig knowledgeBaseStrategyConfig);

    /**
     * 批量删除知识库策略配置
     * 
     * @param ids 需要删除的知识库策略配置主键集合
     * @return 结果
     */
    public int deleteKnowledgeBaseStrategyConfigByIds(Long[] ids);

    /**
     * 删除知识库策略配置信息
     * 
     * @param id 知识库策略配置主键
     * @return 结果
     */
    public int deleteKnowledgeBaseStrategyConfigById(Long id);

    /**
     * 根据知识库ID查询策略配置
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 策略配置列表
     */
    public List<KnowledgeBaseStrategyConfig> selectKnowledgeBaseStrategyConfigByKnowledgeBaseId(Long knowledgeBaseId);

    /**
     * 根据知识库ID和策略类型查询策略配置
     * 
     * @param knowledgeBaseId 知识库ID
     * @param strategyType 策略类型
     * @return 策略配置列表
     */
    public List<KnowledgeBaseStrategyConfig> selectKnowledgeBaseStrategyConfigByKnowledgeBaseIdAndType(Long knowledgeBaseId, String strategyType);

    /**
     * 根据知识库ID删除策略配置
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 结果
     */
    public int deleteKnowledgeBaseStrategyConfigByKnowledgeBaseId(Long knowledgeBaseId);

    /**
     * 查询启用的策略配置
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 启用的策略配置列表
     */
    public List<KnowledgeBaseStrategyConfig> selectEnabledKnowledgeBaseStrategyConfigs(Long knowledgeBaseId);
}
