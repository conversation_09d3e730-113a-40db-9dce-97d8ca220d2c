package com.ruoyi.ai.service.impl;

import java.util.Date;
import java.util.List;
import java.util.UUID;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.ai.mapper.AiChatMessageMapper;
import com.ruoyi.ai.domain.AiChatMessage;
import com.ruoyi.ai.service.IAiChatMessageService;

/**
 * AI聊天消息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-09
 */
@Service
public class AiChatMessageServiceImpl implements IAiChatMessageService 
{
    @Autowired
    private AiChatMessageMapper aiChatMessageMapper;

    /**
     * 查询AI聊天消息
     * 
     * @param messageId AI聊天消息主键
     * @return AI聊天消息
     */
    @Override
    public AiChatMessage selectAiChatMessageByMessageId(String messageId)
    {
        return aiChatMessageMapper.selectAiChatMessageByMessageId(messageId);
    }

    /**
     * 查询AI聊天消息列表
     * 
     * @param aiChatMessage AI聊天消息
     * @return AI聊天消息
     */
    @Override
    public List<AiChatMessage> selectAiChatMessageList(AiChatMessage aiChatMessage)
    {
        return aiChatMessageMapper.selectAiChatMessageList(aiChatMessage);
    }

    /**
     * 根据会话ID查询消息列表
     * 
     * @param sessionId 会话ID
     * @return AI聊天消息集合
     */
    @Override
    public List<AiChatMessage> selectAiChatMessageListBySessionId(String sessionId)
    {
        return aiChatMessageMapper.selectAiChatMessageListBySessionId(sessionId);
    }

    /**
     * 根据会话ID查询最近的消息列表（用于上下文）
     * 
     * @param sessionId 会话ID
     * @param limit 限制数量
     * @return AI聊天消息集合
     */
    @Override
    public List<AiChatMessage> selectRecentMessagesBySessionId(String sessionId, int limit)
    {
        return aiChatMessageMapper.selectRecentMessagesBySessionId(sessionId, limit);
    }

    /**
     * 新增AI聊天消息
     * 
     * @param aiChatMessage AI聊天消息
     * @return 结果
     */
    @Override
    public int insertAiChatMessage(AiChatMessage aiChatMessage)
    {
        aiChatMessage.setCreateTime(new Date());
        return aiChatMessageMapper.insertAiChatMessage(aiChatMessage);
    }

    /**
     * 修改AI聊天消息
     * 
     * @param aiChatMessage AI聊天消息
     * @return 结果
     */
    @Override
    public int updateAiChatMessage(AiChatMessage aiChatMessage)
    {
        aiChatMessage.setUpdateTime(new Date());
        return aiChatMessageMapper.updateAiChatMessage(aiChatMessage);
    }

    /**
     * 批量删除AI聊天消息
     * 
     * @param messageIds 需要删除的AI聊天消息主键
     * @return 结果
     */
    @Override
    public int deleteAiChatMessageByMessageIds(String[] messageIds)
    {
        return aiChatMessageMapper.deleteAiChatMessageByMessageIds(messageIds);
    }

    /**
     * 删除AI聊天消息信息
     * 
     * @param messageId AI聊天消息主键
     * @return 结果
     */
    @Override
    public int deleteAiChatMessageByMessageId(String messageId)
    {
        return aiChatMessageMapper.deleteAiChatMessageByMessageId(messageId);
    }

    /**
     * 根据会话ID删除消息
     * 
     * @param sessionId 会话ID
     * @return 结果
     */
    @Override
    public int deleteAiChatMessageBySessionId(String sessionId)
    {
        return aiChatMessageMapper.deleteAiChatMessageBySessionId(sessionId);
    }

    /**
     * 根据会话ID清空消息（软删除）
     * 
     * @param sessionId 会话ID
     * @return 结果
     */
    @Override
    public int clearAiChatMessageBySessionId(String sessionId)
    {
        return aiChatMessageMapper.clearAiChatMessageBySessionId(sessionId);
    }

    /**
     * 保存用户消息
     * 
     * @param sessionId 会话ID
     * @param content 消息内容
     * @param userId 用户ID
     * @param userName 用户名
     * @param model AI模型
     * @return 消息ID
     */
    @Override
    public String saveUserMessage(String sessionId, String content, Long userId, String userName, String model)
    {
        String messageId = UUID.randomUUID().toString().replace("-", "");
        
        AiChatMessage message = new AiChatMessage();
        message.setMessageId(messageId);
        message.setSessionId(sessionId);
        message.setRole("user");
        message.setContent(content);
        message.setUserId(userId);
        message.setUserName(userName);
        message.setModel(model);
        message.setStatus("0");
        message.setCreateBy(userName);
        message.setCreateTime(new Date());
        
        aiChatMessageMapper.insertAiChatMessage(message);
        return messageId;
    }

    /**
     * 保存AI消息
     * 
     * @param sessionId 会话ID
     * @param content 消息内容
     * @param model AI模型
     * @param responseTime 响应时间
     * @param tokenCount Token数量
     * @return 消息ID
     */
    @Override
    public String saveAiMessage(String sessionId, String content, String model, Long responseTime, Long tokenCount)
    {
        String messageId = UUID.randomUUID().toString().replace("-", "");
        
        AiChatMessage message = new AiChatMessage();
        message.setMessageId(messageId);
        message.setSessionId(sessionId);
        message.setRole("assistant");
        message.setContent(content);
        message.setModel(model);
        message.setStatus("0");
        message.setResponseTime(responseTime);
        message.setTokenCount(tokenCount);
        message.setCreateBy("system");
        message.setCreateTime(new Date());
        
        aiChatMessageMapper.insertAiChatMessage(message);
        return messageId;
    }

    /**
     * 统计会话消息数量
     * 
     * @param sessionId 会话ID
     * @return 消息数量
     */
    @Override
    public int countMessagesBySessionId(String sessionId)
    {
        return aiChatMessageMapper.countMessagesBySessionId(sessionId);
    }
}
