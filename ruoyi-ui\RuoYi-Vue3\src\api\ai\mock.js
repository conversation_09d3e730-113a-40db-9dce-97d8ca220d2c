// AI聊天模块的模拟数据和测试接口
// 用于开发测试，实际部署时应替换为真实的后端接口

// 模拟会话数据
let mockSessions = [
  {
    id: 'session_1',
    title: '关于AI的讨论',
    createTime: new Date(Date.now() - 86400000).toISOString(),
    updateTime: new Date(Date.now() - 3600000).toISOString()
  },
  {
    id: 'session_2', 
    title: '编程问题咨询',
    createTime: new Date(Date.now() - 172800000).toISOString(),
    updateTime: new Date(Date.now() - 7200000).toISOString()
  }
]

// 模拟消息数据
let mockMessages = {
  'session_1': [
    {
      id: 'msg_1',
      role: 'user',
      content: '你好，请介绍一下自己',
      createTime: new Date(Date.now() - 3600000).toISOString()
    },
    {
      id: 'msg_2',
      role: 'assistant', 
      content: '您好！我是AI智能助手，很高兴为您服务。我可以帮助您解答各种问题，包括但不限于：\n\n• 知识问答\n• 编程技术\n• 学习辅导\n• 创意写作\n• 数据分析\n\n请随时告诉我您需要什么帮助！',
      createTime: new Date(Date.now() - 3590000).toISOString()
    }
  ],
  'session_2': [
    {
      id: 'msg_3',
      role: 'user',
      content: '如何在Vue3中使用Composition API？',
      createTime: new Date(Date.now() - 7200000).toISOString()
    },
    {
      id: 'msg_4',
      role: 'assistant',
      content: 'Vue 3的Composition API是一种新的组件逻辑组织方式。以下是基本用法：\n\n```javascript\n<script setup>\nimport { ref, reactive, computed, onMounted } from \'vue\'\n\n// 响应式数据\nconst count = ref(0)\nconst state = reactive({ name: \'Vue3\' })\n\n// 计算属性\nconst doubleCount = computed(() => count.value * 2)\n\n// 方法\nconst increment = () => {\n  count.value++\n}\n\n// 生命周期\nonMounted(() => {\n  console.log(\'组件已挂载\')\n})\n</script>\n```\n\n主要优势：\n1. 更好的逻辑复用\n2. 更好的TypeScript支持\n3. 更灵活的代码组织',
      createTime: new Date(Date.now() - 7190000).toISOString()
    }
  ]
}

// 模拟AI响应
const mockAiResponses = [
  '这是一个很好的问题！让我来为您详细解答...',
  '根据您的描述，我建议您可以尝试以下几种方法：',
  '我理解您的需求，这里有一些实用的建议：',
  '让我为您分析一下这个问题的几个关键点：',
  '基于我的知识，我可以为您提供以下信息：'
]

// 生成随机AI响应
function generateMockResponse(userMessage) {
  const randomResponse = mockAiResponses[Math.floor(Math.random() * mockAiResponses.length)]
  
  // 根据用户消息内容生成更相关的响应
  if (userMessage.includes('编程') || userMessage.includes('代码')) {
    return `${randomResponse}\n\n这是一个编程相关的问题。我建议您：\n1. 先理解基本概念\n2. 查看官方文档\n3. 实践编写代码\n4. 调试和优化\n\n如果您需要具体的代码示例，请告诉我您使用的编程语言和具体需求。`
  }
  
  if (userMessage.includes('学习') || userMessage.includes('教程')) {
    return `${randomResponse}\n\n关于学习，我推荐以下步骤：\n• 制定学习计划\n• 理论与实践结合\n• 定期复习总结\n• 寻求反馈和指导\n\n您想学习哪个具体领域呢？我可以为您提供更详细的学习路径。`
  }
  
  return `${randomResponse}\n\n您的问题很有意思。基于我的理解，我认为关键在于找到合适的方法和角度。如果您能提供更多细节，我可以给出更具体的建议。\n\n还有什么其他问题吗？`
}

// 模拟API延迟
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms))
}

// 导出模拟接口函数
export const mockApi = {
  // 发送消息
  async sendMessage(data) {
    await delay(1000 + Math.random() * 2000) // 模拟1-3秒延迟
    
    const { sessionId, message } = data
    const response = generateMockResponse(message)
    
    // 添加到模拟消息数据
    if (!mockMessages[sessionId]) {
      mockMessages[sessionId] = []
    }
    
    const newMessage = {
      id: `msg_${Date.now()}`,
      role: 'assistant',
      content: response,
      createTime: new Date().toISOString()
    }
    
    mockMessages[sessionId].push(newMessage)
    
    return {
      code: 200,
      msg: 'success',
      data: {
        content: response,
        messageId: newMessage.id
      }
    }
  },

  // 获取聊天历史
  async getChatHistory(params) {
    await delay(300)
    const { sessionId } = params
    
    return {
      code: 200,
      msg: 'success',
      data: mockMessages[sessionId] || []
    }
  },

  // 创建会话
  async createChatSession(data) {
    await delay(500)
    
    const newSession = {
      id: `session_${Date.now()}`,
      title: data.title || '新对话',
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    }
    
    mockSessions.unshift(newSession)
    mockMessages[newSession.id] = []
    
    return {
      code: 200,
      msg: 'success',
      data: newSession
    }
  },

  // 获取会话列表
  async getChatSessions() {
    await delay(300)
    
    return {
      code: 200,
      msg: 'success',
      data: mockSessions
    }
  },

  // 删除会话
  async deleteChatSession(sessionId) {
    await delay(300)
    
    mockSessions = mockSessions.filter(s => s.id !== sessionId)
    delete mockMessages[sessionId]
    
    return {
      code: 200,
      msg: 'success'
    }
  },

  // 清空聊天记录
  async clearChatHistory(sessionId) {
    await delay(300)
    
    mockMessages[sessionId] = []
    
    return {
      code: 200,
      msg: 'success'
    }
  },

  // 获取AI模型列表
  async getAiModels() {
    await delay(200)
    
    return {
      code: 200,
      msg: 'success',
      data: [
        { label: 'qwen-plus', value: 'qwen-plus' }
      ]
    }
  }
}

// 在开发环境中可以使用模拟数据
export const isDevelopment = process.env.NODE_ENV === 'development'
