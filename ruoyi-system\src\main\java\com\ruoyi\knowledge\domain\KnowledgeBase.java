package com.ruoyi.knowledge.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 知识库对象 knowledge_base
 * 
 * <AUTHOR>
 * @date 2024-07-10
 */
public class KnowledgeBase extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 知识库ID */
    private Long id;

    /** 知识库名称 */
    @Excel(name = "知识库名称")
    private String name;

    /** 知识库描述 */
    @Excel(name = "知识库描述")
    private String description;

    /** 所属目录ID */
    @Excel(name = "所属目录ID")
    private Long folderId;

    /** 知识库类型 */
    @Excel(name = "知识库类型")
    private String type;

    /** 知识库状态（0正常 1停用） */
    @Excel(name = "知识库状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 文档数量 */
    @Excel(name = "文档数量")
    private Long documentCount;

    /** 知识库大小（字节） */
    @Excel(name = "知识库大小")
    private Long size;

    /** 最后更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最后更新时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date lastUpdateTime;

    /** 创建者ID */
    @Excel(name = "创建者ID")
    private Long creatorId;

    /** 创建者名称 */
    @Excel(name = "创建者名称")
    private String creatorName;

    /** 辅助字段：知识库ID数组（用于查询） */
    private Long[] ids;

    /** 辅助字段：排除的创建者ID（用于查询） */
    private Long excludeCreatorId;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }
    public void setDescription(String description) 
    {
        this.description = description;
    }

    public String getDescription() 
    {
        return description;
    }
    public void setFolderId(Long folderId) 
    {
        this.folderId = folderId;
    }

    public Long getFolderId() 
    {
        return folderId;
    }
    public void setType(String type) 
    {
        this.type = type;
    }

    public String getType() 
    {
        return type;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setDocumentCount(Long documentCount) 
    {
        this.documentCount = documentCount;
    }

    public Long getDocumentCount() 
    {
        return documentCount;
    }
    public void setSize(Long size) 
    {
        this.size = size;
    }

    public Long getSize() 
    {
        return size;
    }
    public void setLastUpdateTime(Date lastUpdateTime) 
    {
        this.lastUpdateTime = lastUpdateTime;
    }

    public Date getLastUpdateTime() 
    {
        return lastUpdateTime;
    }
    public void setCreatorId(Long creatorId) 
    {
        this.creatorId = creatorId;
    }

    public Long getCreatorId() 
    {
        return creatorId;
    }
    public void setCreatorName(String creatorName) 
    {
        this.creatorName = creatorName;
    }

    public String getCreatorName()
    {
        return creatorName;
    }

    public void setIds(Long[] ids)
    {
        this.ids = ids;
    }

    public Long[] getIds()
    {
        return ids;
    }

    public void setExcludeCreatorId(Long excludeCreatorId)
    {
        this.excludeCreatorId = excludeCreatorId;
    }

    public Long getExcludeCreatorId()
    {
        return excludeCreatorId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("name", getName())
            .append("description", getDescription())
            .append("folderId", getFolderId())
            .append("type", getType())
            .append("status", getStatus())
            .append("documentCount", getDocumentCount())
            .append("size", getSize())
            .append("lastUpdateTime", getLastUpdateTime())
            .append("creatorId", getCreatorId())
            .append("creatorName", getCreatorName())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
