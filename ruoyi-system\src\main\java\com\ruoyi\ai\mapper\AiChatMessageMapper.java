package com.ruoyi.ai.mapper;

import java.util.List;
import com.ruoyi.ai.domain.AiChatMessage;
import org.apache.ibatis.annotations.Param;

/**
 * AI聊天消息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-09
 */
public interface AiChatMessageMapper {
    /**
     * 查询AI聊天消息
     * 
     * @param messageId AI聊天消息主键
     * @return AI聊天消息
     */
    public AiChatMessage selectAiChatMessageByMessageId(String messageId);

    /**
     * 查询AI聊天消息列表
     * 
     * @param aiChatMessage AI聊天消息
     * @return AI聊天消息集合
     */
    public List<AiChatMessage> selectAiChatMessageList(AiChatMessage aiChatMessage);

    /**
     * 根据会话ID查询消息列表
     * 
     * @param sessionId 会话ID
     * @return AI聊天消息集合
     */
    public List<AiChatMessage> selectAiChatMessageListBySessionId(String sessionId);

    /**
     * 根据会话ID查询最近的消息列表（用于上下文）
     *
     * @param sessionId 会话ID
     * @param limit     限制数量
     * @return AI聊天消息集合
     */
    public List<AiChatMessage> selectRecentMessagesBySessionId(@Param("sessionId") String sessionId,
            @Param("limit") int limit);

    /**
     * 新增AI聊天消息
     * 
     * @param aiChatMessage AI聊天消息
     * @return 结果
     */
    public int insertAiChatMessage(AiChatMessage aiChatMessage);

    /**
     * 修改AI聊天消息
     * 
     * @param aiChatMessage AI聊天消息
     * @return 结果
     */
    public int updateAiChatMessage(AiChatMessage aiChatMessage);

    /**
     * 删除AI聊天消息
     * 
     * @param messageId AI聊天消息主键
     * @return 结果
     */
    public int deleteAiChatMessageByMessageId(String messageId);

    /**
     * 批量删除AI聊天消息
     * 
     * @param messageIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAiChatMessageByMessageIds(String[] messageIds);

    /**
     * 根据会话ID删除消息
     * 
     * @param sessionId 会话ID
     * @return 结果
     */
    public int deleteAiChatMessageBySessionId(String sessionId);

    /**
     * 根据会话ID清空消息（软删除）
     * 
     * @param sessionId 会话ID
     * @return 结果
     */
    public int clearAiChatMessageBySessionId(String sessionId);

    /**
     * 统计会话消息数量
     * 
     * @param sessionId 会话ID
     * @return 消息数量
     */
    public int countMessagesBySessionId(String sessionId);
}
