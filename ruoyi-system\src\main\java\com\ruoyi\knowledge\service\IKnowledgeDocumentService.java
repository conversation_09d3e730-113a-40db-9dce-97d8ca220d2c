package com.ruoyi.knowledge.service;

import java.util.List;
import com.ruoyi.knowledge.domain.KnowledgeDocument;
import org.springframework.web.multipart.MultipartFile;

/**
 * 知识库文档Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-10
 */
public interface IKnowledgeDocumentService 
{
    /**
     * 查询知识库文档
     * 
     * @param id 知识库文档主键
     * @return 知识库文档
     */
    public KnowledgeDocument selectKnowledgeDocumentById(Long id);

    /**
     * 查询知识库文档列表
     * 
     * @param knowledgeDocument 知识库文档
     * @return 知识库文档集合
     */
    public List<KnowledgeDocument> selectKnowledgeDocumentList(KnowledgeDocument knowledgeDocument);

    /**
     * 根据文件夹ID查询文档列表
     * 
     * @param folderId 文件夹ID
     * @return 知识库文档集合
     */
    public List<KnowledgeDocument> selectKnowledgeDocumentByFolderId(Long folderId);

    /**
     * 根据知识库ID查询文档列表
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 知识库文档集合
     */
    public List<KnowledgeDocument> selectKnowledgeDocumentByKnowledgeBaseId(Long knowledgeBaseId);

    /**
     * 根据关键词搜索文档
     * 
     * @param keyword 关键词
     * @return 知识库文档集合
     */
    public List<KnowledgeDocument> searchKnowledgeDocumentByKeyword(String keyword);

    /**
     * 新增知识库文档
     * 
     * @param knowledgeDocument 知识库文档
     * @return 结果
     */
    public int insertKnowledgeDocument(KnowledgeDocument knowledgeDocument);

    /**
     * 修改知识库文档
     * 
     * @param knowledgeDocument 知识库文档
     * @return 结果
     */
    public int updateKnowledgeDocument(KnowledgeDocument knowledgeDocument);

    /**
     * 批量删除知识库文档
     * 
     * @param ids 需要删除的知识库文档主键集合
     * @return 结果
     */
    public int deleteKnowledgeDocumentByIds(Long[] ids);

    /**
     * 删除知识库文档信息
     * 
     * @param id 知识库文档主键
     * @return 结果
     */
    public int deleteKnowledgeDocumentById(Long id);

    /**
     * 上传文档
     * 
     * @param file 上传的文件
     * @param folderId 文件夹ID
     * @param knowledgeBaseId 知识库ID
     * @return 结果
     */
    public KnowledgeDocument uploadDocument(MultipartFile file, Long folderId, Long knowledgeBaseId);

    /**
     * 批量上传文档
     * 
     * @param files 上传的文件列表
     * @param folderId 文件夹ID
     * @param knowledgeBaseId 知识库ID
     * @return 结果
     */
    public List<KnowledgeDocument> batchUploadDocuments(MultipartFile[] files, Long folderId, Long knowledgeBaseId);

    /**
     * 处理文档内容
     * 
     * @param id 文档ID
     * @return 结果
     */
    public boolean processDocument(Long id);

    /**
     * 更新文档访问次数
     * 
     * @param id 文档ID
     * @return 结果
     */
    public int updateViewCount(Long id);

    /**
     * 更新文档下载次数
     * 
     * @param id 文档ID
     * @return 结果
     */
    public int updateDownloadCount(Long id);

    /**
     * 获取文档内容
     * 
     * @param id 文档ID
     * @return 文档内容
     */
    public String getDocumentContent(Long id);

    /**
     * 导出文档
     * 
     * @param ids 文档ID列表
     * @return 导出结果
     */
    public String exportDocuments(Long[] ids);
}
