package com.ruoyi.knowledge.service.impl;

import java.util.List;
import java.util.ArrayList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.core.domain.TreeSelect;
import com.ruoyi.knowledge.mapper.KnowledgeFolderMapper;
import com.ruoyi.knowledge.domain.KnowledgeFolder;
import com.ruoyi.knowledge.service.IKnowledgeFolderService;

/**
 * 知识库文件夹Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-10
 */
@Service
public class KnowledgeFolderServiceImpl implements IKnowledgeFolderService {
    @Autowired
    private KnowledgeFolderMapper knowledgeFolderMapper;

    /**
     * 查询知识库文件夹
     * 
     * @param id 知识库文件夹主键
     * @return 知识库文件夹
     */
    @Override
    public KnowledgeFolder selectKnowledgeFolderById(Long id) {
        return knowledgeFolderMapper.selectKnowledgeFolderById(id);
    }

    /**
     * 查询知识库文件夹列表
     * 
     * @param knowledgeFolder 知识库文件夹
     * @return 知识库文件夹
     */
    @Override
    public List<KnowledgeFolder> selectKnowledgeFolderList(KnowledgeFolder knowledgeFolder) {
        return knowledgeFolderMapper.selectKnowledgeFolderList(knowledgeFolder);
    }

    /**
     * 查询知识库文件夹树结构
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 知识库文件夹集合
     */
    @Override
    public List<KnowledgeFolder> selectKnowledgeFolderTree(Long knowledgeBaseId) {
        List<KnowledgeFolder> folderList = knowledgeFolderMapper.selectKnowledgeFolderTree(knowledgeBaseId);
        return buildFolderTree(folderList, 0L);
    }

    /**
     * 构建前端所需要的树结构
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 树结构列表
     */
    @Override
    public List<TreeSelect> buildFolderTreeSelect(Long knowledgeBaseId) {
        List<KnowledgeFolder> folderTree = selectKnowledgeFolderTree(knowledgeBaseId);
        return buildFolderTreeSelect(folderTree);
    }

    /**
     * 根据父文件夹ID查询子文件夹
     * 
     * @param parentId 父文件夹ID
     * @return 知识库文件夹集合
     */
    @Override
    public List<KnowledgeFolder> selectKnowledgeFolderByParentId(Long parentId) {
        return knowledgeFolderMapper.selectKnowledgeFolderByParentId(parentId);
    }

    /**
     * 根据知识库ID查询根文件夹
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 知识库文件夹集合
     */
    @Override
    public List<KnowledgeFolder> selectRootFoldersByKnowledgeBaseId(Long knowledgeBaseId) {
        return knowledgeFolderMapper.selectRootFoldersByKnowledgeBaseId(knowledgeBaseId);
    }

    /**
     * 新增知识库文件夹
     * 
     * @param knowledgeFolder 知识库文件夹
     * @return 结果
     */
    @Override
    public int insertKnowledgeFolder(KnowledgeFolder knowledgeFolder) {
        knowledgeFolder.setCreateTime(DateUtils.getNowDate());
        knowledgeFolder.setCreateBy(SecurityUtils.getUsername());
        knowledgeFolder.setCreatorId(SecurityUtils.getUserId());
        knowledgeFolder.setCreatorName(SecurityUtils.getUsername());

        // 设置文件夹路径和层级
        if (knowledgeFolder.getParentId() != null && knowledgeFolder.getParentId() != 0) {
            KnowledgeFolder parentFolder = selectKnowledgeFolderById(knowledgeFolder.getParentId());
            if (parentFolder != null) {
                knowledgeFolder.setPath(parentFolder.getPath() + "/" + knowledgeFolder.getName());
                knowledgeFolder.setLevel(parentFolder.getLevel() + 1);
            }
        } else {
            knowledgeFolder.setPath("/" + knowledgeFolder.getName());
            knowledgeFolder.setLevel(1);
        }

        return knowledgeFolderMapper.insertKnowledgeFolder(knowledgeFolder);
    }

    /**
     * 修改知识库文件夹
     * 
     * @param knowledgeFolder 知识库文件夹
     * @return 结果
     */
    @Override
    public int updateKnowledgeFolder(KnowledgeFolder knowledgeFolder) {
        knowledgeFolder.setUpdateTime(DateUtils.getNowDate());
        knowledgeFolder.setUpdateBy(SecurityUtils.getUsername());
        return knowledgeFolderMapper.updateKnowledgeFolder(knowledgeFolder);
    }

    /**
     * 批量删除知识库文件夹
     * 
     * @param ids 需要删除的知识库文件夹主键
     * @return 结果
     */
    @Override
    public int deleteKnowledgeFolderByIds(Long[] ids) {
        return knowledgeFolderMapper.deleteKnowledgeFolderByIds(ids);
    }

    /**
     * 删除知识库文件夹信息
     * 
     * @param id 知识库文件夹主键
     * @return 结果
     */
    @Override
    public int deleteKnowledgeFolderById(Long id) {
        return knowledgeFolderMapper.deleteKnowledgeFolderById(id);
    }

    /**
     * 检查文件夹名称是否唯一
     * 
     * @param knowledgeFolder 知识库文件夹
     * @return 结果
     */
    @Override
    public boolean checkFolderNameUnique(KnowledgeFolder knowledgeFolder) {
        int count = knowledgeFolderMapper.checkFolderNameUnique(knowledgeFolder);
        return count == 0;
    }

    /**
     * 更新文件夹统计信息
     * 
     * @param folderId 文件夹ID
     * @return 结果
     */
    @Override
    public int updateFolderStatistics(Long folderId) {
        // TODO: 实现统计信息更新逻辑
        return 1;
    }

    /**
     * 移动文件夹
     * 
     * @param folderId    文件夹ID
     * @param newParentId 新父文件夹ID
     * @return 结果
     */
    @Override
    public int moveFolder(Long folderId, Long newParentId) {
        // TODO: 实现文件夹移动逻辑
        return 1;
    }

    /**
     * 复制文件夹
     * 
     * @param folderId    文件夹ID
     * @param newParentId 新父文件夹ID
     * @param newName     新名称
     * @return 结果
     */
    @Override
    public int copyFolder(Long folderId, Long newParentId, String newName) {
        // TODO: 实现文件夹复制逻辑
        return 1;
    }

    /**
     * 构建文件夹树结构
     * 
     * @param folders  文件夹列表
     * @param parentId 父文件夹ID
     * @return 树结构
     */
    private List<KnowledgeFolder> buildFolderTree(List<KnowledgeFolder> folders, Long parentId) {
        List<KnowledgeFolder> returnList = new ArrayList<>();
        List<Long> tempList = new ArrayList<>();
        for (KnowledgeFolder folder : folders) {
            tempList.add(folder.getId());
        }
        for (KnowledgeFolder folder : folders) {
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(folder.getParentId())) {
                recursionFn(folders, folder);
                returnList.add(folder);
            }
        }
        if (returnList.isEmpty()) {
            returnList = folders;
        }
        return returnList;
    }

    /**
     * 递归列表
     */
    private void recursionFn(List<KnowledgeFolder> list, KnowledgeFolder folder) {
        // 得到子节点列表
        List<KnowledgeFolder> childList = getChildList(list, folder);
        folder.setChildren(childList);
        for (KnowledgeFolder tChild : childList) {
            if (hasChild(list, tChild)) {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<KnowledgeFolder> getChildList(List<KnowledgeFolder> list, KnowledgeFolder folder) {
        List<KnowledgeFolder> tlist = new ArrayList<>();
        for (KnowledgeFolder n : list) {
            if (StringUtils.isNotNull(n.getParentId()) && n.getParentId().longValue() == folder.getId().longValue()) {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<KnowledgeFolder> list, KnowledgeFolder folder) {
        return getChildList(list, folder).size() > 0;
    }

    /**
     * 构建前端所需要下拉树结构
     *
     * @param folders 文件夹列表
     * @return 下拉树结构列表
     */
    private List<TreeSelect> buildFolderTreeSelect(List<KnowledgeFolder> folders) {
        List<KnowledgeFolder> folderTrees = buildFolderTree(folders, 0L);
        return folderTrees.stream().map(this::buildTreeSelect).collect(java.util.stream.Collectors.toList());
    }

    /**
     * 构建TreeSelect对象
     *
     * @param folder 文件夹对象
     * @return TreeSelect对象
     */
    private TreeSelect buildTreeSelect(KnowledgeFolder folder) {
        TreeSelect treeSelect = new TreeSelect();
        treeSelect.setId(folder.getId());
        treeSelect.setLabel(folder.getName());
        treeSelect.setDisabled(StringUtils.equals("1", folder.getStatus()));
        if (folder.getChildren() != null && !folder.getChildren().isEmpty()) {
            treeSelect.setChildren(folder.getChildren().stream().map(this::buildTreeSelect)
                    .collect(java.util.stream.Collectors.toList()));
        }
        return treeSelect;
    }
}
