package com.ruoyi.knowledge.service;

import java.util.List;
import com.ruoyi.knowledge.domain.KnowledgeFolder;
import com.ruoyi.common.core.domain.TreeSelect;

/**
 * 知识库文件夹Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-10
 */
public interface IKnowledgeFolderService 
{
    /**
     * 查询知识库文件夹
     * 
     * @param id 知识库文件夹主键
     * @return 知识库文件夹
     */
    public KnowledgeFolder selectKnowledgeFolderById(Long id);

    /**
     * 查询知识库文件夹列表
     * 
     * @param knowledgeFolder 知识库文件夹
     * @return 知识库文件夹集合
     */
    public List<KnowledgeFolder> selectKnowledgeFolderList(KnowledgeFolder knowledgeFolder);

    /**
     * 查询知识库文件夹树结构
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 知识库文件夹集合
     */
    public List<KnowledgeFolder> selectKnowledgeFolderTree(Long knowledgeBaseId);

    /**
     * 构建前端所需要的树结构
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 树结构列表
     */
    public List<TreeSelect> buildFolderTreeSelect(Long knowledgeBaseId);

    /**
     * 根据父文件夹ID查询子文件夹
     * 
     * @param parentId 父文件夹ID
     * @return 知识库文件夹集合
     */
    public List<KnowledgeFolder> selectKnowledgeFolderByParentId(Long parentId);

    /**
     * 根据知识库ID查询根文件夹
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 知识库文件夹集合
     */
    public List<KnowledgeFolder> selectRootFoldersByKnowledgeBaseId(Long knowledgeBaseId);

    /**
     * 新增知识库文件夹
     * 
     * @param knowledgeFolder 知识库文件夹
     * @return 结果
     */
    public int insertKnowledgeFolder(KnowledgeFolder knowledgeFolder);

    /**
     * 修改知识库文件夹
     * 
     * @param knowledgeFolder 知识库文件夹
     * @return 结果
     */
    public int updateKnowledgeFolder(KnowledgeFolder knowledgeFolder);

    /**
     * 批量删除知识库文件夹
     * 
     * @param ids 需要删除的知识库文件夹主键集合
     * @return 结果
     */
    public int deleteKnowledgeFolderByIds(Long[] ids);

    /**
     * 删除知识库文件夹信息
     * 
     * @param id 知识库文件夹主键
     * @return 结果
     */
    public int deleteKnowledgeFolderById(Long id);

    /**
     * 检查文件夹名称是否唯一
     * 
     * @param knowledgeFolder 知识库文件夹
     * @return 结果
     */
    public boolean checkFolderNameUnique(KnowledgeFolder knowledgeFolder);

    /**
     * 更新文件夹统计信息
     * 
     * @param folderId 文件夹ID
     * @return 结果
     */
    public int updateFolderStatistics(Long folderId);

    /**
     * 移动文件夹
     * 
     * @param folderId 文件夹ID
     * @param newParentId 新父文件夹ID
     * @return 结果
     */
    public int moveFolder(Long folderId, Long newParentId);

    /**
     * 复制文件夹
     * 
     * @param folderId 文件夹ID
     * @param newParentId 新父文件夹ID
     * @param newName 新名称
     * @return 结果
     */
    public int copyFolder(Long folderId, Long newParentId, String newName);
}
