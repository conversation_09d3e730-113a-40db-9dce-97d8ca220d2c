package com.ruoyi.knowledge.service;

import com.ruoyi.knowledge.domain.KnowledgeBaseStrategyConfig;
import com.ruoyi.knowledge.strategy.StrategyExecutor;
import java.util.List;

/**
 * 知识库策略管理Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
public interface IKnowledgeStrategyService {
    
    /**
     * 为知识库配置策略
     * 
     * @param knowledgeBaseId 知识库ID
     * @param strategyConfigs 策略配置列表
     * @return 配置结果
     */
    boolean configureStrategiesForKnowledgeBase(Long knowledgeBaseId, List<KnowledgeBaseStrategyConfig> strategyConfigs);
    
    /**
     * 获取知识库的策略配置
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 策略配置列表
     */
    List<KnowledgeBaseStrategyConfig> getKnowledgeBaseStrategyConfigs(Long knowledgeBaseId);
    
    /**
     * 执行知识库策略
     * 
     * @param knowledgeBaseId 知识库ID
     * @param input 输入数据
     * @param parallel 是否并行执行
     * @return 执行结果列表
     */
    List<StrategyExecutor.StrategyExecutionResult> executeKnowledgeBaseStrategies(Long knowledgeBaseId, Object input, boolean parallel);
    
    /**
     * 执行指定类型的策略
     * 
     * @param knowledgeBaseId 知识库ID
     * @param strategyType 策略类型
     * @param input 输入数据
     * @return 执行结果列表
     */
    List<StrategyExecutor.StrategyExecutionResult> executeStrategiesByType(Long knowledgeBaseId, String strategyType, Object input);
    
    /**
     * 启用/禁用策略
     * 
     * @param configId 策略配置ID
     * @param enabled 是否启用
     * @return 操作结果
     */
    boolean toggleStrategyConfig(Long configId, boolean enabled);
    
    /**
     * 更新策略配置
     * 
     * @param config 策略配置
     * @return 更新结果
     */
    boolean updateStrategyConfig(KnowledgeBaseStrategyConfig config);
    
    /**
     * 删除知识库的所有策略配置
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 删除结果
     */
    boolean deleteKnowledgeBaseStrategyConfigs(Long knowledgeBaseId);
    
    /**
     * 获取可用的策略类型
     * 
     * @return 策略类型列表
     */
    List<String> getAvailableStrategyTypes();
    
    /**
     * 验证策略配置
     * 
     * @param strategyType 策略类型
     * @param strategyName 策略名称
     * @param configJson 配置JSON
     * @return 是否有效
     */
    boolean validateStrategyConfig(String strategyType, String strategyName, String configJson);
}
