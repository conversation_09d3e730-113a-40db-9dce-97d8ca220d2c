package com.ruoyi.knowledge.service.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.knowledge.domain.KnowledgeBaseStrategyConfig;
import com.ruoyi.knowledge.mapper.KnowledgeBaseStrategyConfigMapper;
import com.ruoyi.knowledge.service.IKnowledgeBaseStrategyConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 知识库策略配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
@Service
public class KnowledgeBaseStrategyConfigServiceImpl implements IKnowledgeBaseStrategyConfigService 
{
    @Autowired
    private KnowledgeBaseStrategyConfigMapper knowledgeBaseStrategyConfigMapper;

    /**
     * 查询知识库策略配置
     * 
     * @param id 知识库策略配置主键
     * @return 知识库策略配置
     */
    @Override
    public KnowledgeBaseStrategyConfig selectKnowledgeBaseStrategyConfigById(Long id)
    {
        return knowledgeBaseStrategyConfigMapper.selectKnowledgeBaseStrategyConfigById(id);
    }

    /**
     * 查询知识库策略配置列表
     * 
     * @param knowledgeBaseStrategyConfig 知识库策略配置
     * @return 知识库策略配置
     */
    @Override
    public List<KnowledgeBaseStrategyConfig> selectKnowledgeBaseStrategyConfigList(KnowledgeBaseStrategyConfig knowledgeBaseStrategyConfig)
    {
        return knowledgeBaseStrategyConfigMapper.selectKnowledgeBaseStrategyConfigList(knowledgeBaseStrategyConfig);
    }

    /**
     * 新增知识库策略配置
     * 
     * @param knowledgeBaseStrategyConfig 知识库策略配置
     * @return 结果
     */
    @Override
    public int insertKnowledgeBaseStrategyConfig(KnowledgeBaseStrategyConfig knowledgeBaseStrategyConfig)
    {
        knowledgeBaseStrategyConfig.setCreateTime(DateUtils.getNowDate());
        knowledgeBaseStrategyConfig.setCreateBy(SecurityUtils.getUsername());
        return knowledgeBaseStrategyConfigMapper.insertKnowledgeBaseStrategyConfig(knowledgeBaseStrategyConfig);
    }

    /**
     * 修改知识库策略配置
     * 
     * @param knowledgeBaseStrategyConfig 知识库策略配置
     * @return 结果
     */
    @Override
    public int updateKnowledgeBaseStrategyConfig(KnowledgeBaseStrategyConfig knowledgeBaseStrategyConfig)
    {
        knowledgeBaseStrategyConfig.setUpdateTime(DateUtils.getNowDate());
        knowledgeBaseStrategyConfig.setUpdateBy(SecurityUtils.getUsername());
        return knowledgeBaseStrategyConfigMapper.updateKnowledgeBaseStrategyConfig(knowledgeBaseStrategyConfig);
    }

    /**
     * 批量删除知识库策略配置
     * 
     * @param ids 需要删除的知识库策略配置主键
     * @return 结果
     */
    @Override
    public int deleteKnowledgeBaseStrategyConfigByIds(Long[] ids)
    {
        return knowledgeBaseStrategyConfigMapper.deleteKnowledgeBaseStrategyConfigByIds(ids);
    }

    /**
     * 删除知识库策略配置信息
     * 
     * @param id 知识库策略配置主键
     * @return 结果
     */
    @Override
    public int deleteKnowledgeBaseStrategyConfigById(Long id)
    {
        return knowledgeBaseStrategyConfigMapper.deleteKnowledgeBaseStrategyConfigById(id);
    }

    @Override
    public List<KnowledgeBaseStrategyConfig> selectKnowledgeBaseStrategyConfigByKnowledgeBaseId(Long knowledgeBaseId) {
        return knowledgeBaseStrategyConfigMapper.selectKnowledgeBaseStrategyConfigByKnowledgeBaseId(knowledgeBaseId);
    }

    @Override
    public List<KnowledgeBaseStrategyConfig> selectKnowledgeBaseStrategyConfigByKnowledgeBaseIdAndType(Long knowledgeBaseId, String strategyType) {
        return knowledgeBaseStrategyConfigMapper.selectKnowledgeBaseStrategyConfigByKnowledgeBaseIdAndType(knowledgeBaseId, strategyType);
    }

    @Override
    public int deleteKnowledgeBaseStrategyConfigByKnowledgeBaseId(Long knowledgeBaseId) {
        return knowledgeBaseStrategyConfigMapper.deleteKnowledgeBaseStrategyConfigByKnowledgeBaseId(knowledgeBaseId);
    }

    @Override
    public List<KnowledgeBaseStrategyConfig> selectEnabledKnowledgeBaseStrategyConfigs(Long knowledgeBaseId) {
        return knowledgeBaseStrategyConfigMapper.selectEnabledKnowledgeBaseStrategyConfigs(knowledgeBaseId);
    }
}
