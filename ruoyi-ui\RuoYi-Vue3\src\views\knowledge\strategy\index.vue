<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="策略名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入策略名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="策略类型" prop="strategyType">
        <el-select v-model="queryParams.strategyType" placeholder="请选择策略类型" clearable>
          <el-option
            v-for="type in strategyTypes"
            :key="type.code"
            :label="type.name"
            :value="type.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="isEnabled">
        <el-select v-model="queryParams.isEnabled" placeholder="策略状态" clearable>
          <el-option label="启用" value="1" />
          <el-option label="禁用" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['knowledge:strategy:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['knowledge:strategy:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['knowledge:strategy:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['knowledge:strategy:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="strategyList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="策略ID" align="center" prop="id" />
      <el-table-column label="策略名称" align="center" prop="name" />
      <el-table-column label="策略类型" align="center" prop="strategyType">
        <template #default="scope">
          <el-tag :type="getStrategyTypeTag(scope.row.strategyType)">
            {{ getStrategyTypeName(scope.row.strategyType) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="策略描述" align="center" prop="description" :show-overflow-tooltip="true" />
      <el-table-column label="是否默认" align="center" prop="isDefault">
        <template #default="scope">
          <el-tag :type="scope.row.isDefault === '1' ? 'success' : 'info'">
            {{ scope.row.isDefault === '1' ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="isEnabled">
        <template #default="scope">
          <el-switch
            v-model="scope.row.isEnabled"
            active-value="1"
            inactive-value="0"
            @change="handleStatusChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['knowledge:strategy:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['knowledge:strategy:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改策略模板对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="strategyRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="策略名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入策略名称" />
        </el-form-item>
        <el-form-item label="策略类型" prop="strategyType">
          <el-select v-model="form.strategyType" placeholder="请选择策略类型">
            <el-option
              v-for="type in strategyTypes"
              :key="type.code"
              :label="type.name"
              :value="type.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="策略描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入策略描述" />
        </el-form-item>
        <el-form-item label="配置JSON" prop="configJson">
          <el-input v-model="form.configJson" type="textarea" :rows="6" placeholder="请输入策略配置JSON" />
        </el-form-item>
        <el-form-item label="是否默认" prop="isDefault">
          <el-radio-group v-model="form.isDefault">
            <el-radio label="1">是</el-radio>
            <el-radio label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="状态" prop="isEnabled">
          <el-radio-group v-model="form.isEnabled">
            <el-radio label="1">启用</el-radio>
            <el-radio label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="排序" prop="sortOrder">
          <el-input-number v-model="form.sortOrder" controls-position="right" :min="0" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="KnowledgeStrategy">
import { 
  listStrategyTemplate, 
  getStrategyTemplate, 
  delStrategyTemplate, 
  addStrategyTemplate, 
  updateStrategyTemplate,
  getStrategyTypes
} from "@/api/knowledge/strategy";

const { proxy } = getCurrentInstance();
const { parseTime } = proxy.useDict();

const strategyList = ref([]);
const strategyTypes = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    name: null,
    strategyType: null,
    isEnabled: null
  },
  rules: {
    name: [
      { required: true, message: "策略名称不能为空", trigger: "blur" }
    ],
    strategyType: [
      { required: true, message: "策略类型不能为空", trigger: "change" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询策略模板列表 */
function getList() {
  loading.value = true;
  listStrategyTemplate(queryParams.value).then(response => {
    strategyList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 加载策略类型 */
function loadStrategyTypes() {
  getStrategyTypes().then(response => {
    if (response.code === 200) {
      strategyTypes.value = response.data;
    }
  });
}

// 策略类型标签颜色
function getStrategyTypeTag(strategyType) {
  const tagMap = {
    'INITIALIZATION': 'success',
    'SEGMENTATION': 'primary',
    'TAGGING': 'warning',
    'SUMMARIZATION': 'info',
    'ASSOCIATION': 'danger'
  };
  return tagMap[strategyType] || 'info';
}

// 策略类型名称
function getStrategyTypeName(strategyType) {
  const type = strategyTypes.value.find(t => t.code === strategyType);
  return type ? type.name : strategyType;
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    id: null,
    name: null,
    description: null,
    strategyType: null,
    configJson: null,
    isDefault: "0",
    isEnabled: "1",
    sortOrder: 0
  };
  proxy.resetForm("strategyRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加策略模板";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const id = row.id || ids.value;
  getStrategyTemplate(id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改策略模板";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["strategyRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateStrategyTemplate(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addStrategyTemplate(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const strategyIds = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除策略模板编号为"' + strategyIds + '"的数据项？').then(function() {
    return delStrategyTemplate(strategyIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 状态修改 */
function handleStatusChange(row) {
  let text = row.isEnabled === "1" ? "启用" : "停用";
  proxy.$modal.confirm('确认要"' + text + '""' + row.name + '"策略吗？').then(function() {
    return updateStrategyTemplate(row);
  }).then(() => {
    proxy.$modal.msgSuccess(text + "成功");
  }).catch(function() {
    row.isEnabled = row.isEnabled === "0" ? "1" : "0";
  });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('knowledge/strategy/template/export', {
    ...queryParams.value
  }, `strategy_template_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
  loadStrategyTypes();
});
</script>
