package com.ruoyi.ai.config;

import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.data.message.ChatMessage;
import dev.langchain4j.memory.ChatMemory;
import dev.langchain4j.memory.chat.MessageWindowChatMemory;
import dev.langchain4j.model.StreamingResponseHandler;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.chat.StreamingChatLanguageModel;
import dev.langchain4j.model.openai.OpenAiChatModel;
import dev.langchain4j.model.openai.OpenAiStreamingChatModel;
import dev.langchain4j.service.AiServices;
import dev.langchain4j.service.SystemMessage;
import dev.langchain4j.service.UserMessage;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * AI配置类
 * 
 * <AUTHOR>
 * @date 2024-07-09
 */
@Configuration
public class AiConfig {

    @Value("${langchain4j.open-ai.chat-model.api-key}")
    private String apiKey;

    @Value("${langchain4j.open-ai.chat-model.base-url}")
    private String baseUrl;

    @Value("${langchain4j.open-ai.chat-model.model-name:qwen-plus}")
    private String modelName;

    /**
     * 聊天语言模型Bean
     */
    @Bean
    public ChatLanguageModel chatLanguageModel() {
        return OpenAiChatModel.builder()
                .apiKey(apiKey)
                .baseUrl(baseUrl)
                .modelName(modelName)
                .timeout(Duration.ofSeconds(150))
                .maxTokens(2000)
                .temperature(0.7)
                .logRequests(true)
                .logResponses(true)
                .build();
    }

    /**
     * 流式聊天语言模型Bean
     */
    @Bean
    public StreamingChatLanguageModel streamingChatLanguageModel() {
        return OpenAiStreamingChatModel.builder()
                .apiKey(apiKey)
                .baseUrl(baseUrl)
                .modelName(modelName)
                .timeout(Duration.ofSeconds(150))
                .maxTokens(2000)
                .temperature(0.7)
                .logRequests(true)
                .logResponses(true)
                .build();
    }

    /**
     * 会话记忆存储
     */
    private final ConcurrentHashMap<String, ChatMemory> sessionMemories = new ConcurrentHashMap<>();

    /**
     * 获取或创建会话记忆
     */
    public ChatMemory getOrCreateChatMemory(String sessionId) {
        return sessionMemories.computeIfAbsent(sessionId,
                k -> MessageWindowChatMemory.withMaxMessages(20));
    }

    /**
     * 清除会话记忆
     */
    public void clearChatMemory(String sessionId) {
        sessionMemories.remove(sessionId);
    }

    /**
     * AI助手接口
     */
    public interface AiAssistant {

        @SystemMessage("你是一个智能助手，名字叫小若。你需要：" +
                "1. 用友好、专业的语气回答用户问题" +
                "2. 如果不确定答案，请诚实说明" +
                "3. 尽量提供有用、准确的信息" +
                "4. 保持回答简洁明了，避免冗长")
        String chat(@UserMessage String userMessage);

        @SystemMessage("你是一个智能助手，名字叫小若。你需要：" +
                "1. 用友好、专业的语气回答用户问题" +
                "2. 如果不确定答案，请诚实说明" +
                "3. 尽量提供有用、准确的信息" +
                "4. 保持回答简洁明了，避免冗长")
        void chatStream(@UserMessage String userMessage, StreamingResponseHandler<AiMessage> handler);
    }

    /**
     * 创建AI助手实例（同步）
     */
    public AiAssistant createAiAssistant(ChatMemory chatMemory) {
        return AiServices.builder(AiAssistant.class)
                .chatLanguageModel(chatLanguageModel())
                .chatMemory(chatMemory)
                .build();
    }

    /**
     * 创建AI助手实例（流式）
     */
    public AiAssistant createStreamingAiAssistant(ChatMemory chatMemory) {
        return AiServices.builder(AiAssistant.class)
                .streamingChatLanguageModel(streamingChatLanguageModel())
                .chatMemory(chatMemory)
                .build();
    }
}
