import request from '@/utils/request'

// 查询知识库权限列表
export function listPermission(query) {
  return request({
    url: '/knowledge/permission/list',
    method: 'get',
    params: query
  })
}

// 根据知识库ID查询权限列表
export function listPermissionByKnowledgeBase(knowledgeBaseId) {
  return request({
    url: '/knowledge/permission/listByKnowledgeBase/' + knowledgeBaseId,
    method: 'get'
  })
}

// 查询当前用户的知识库权限列表
export function getMyPermissions() {
  return request({
    url: '/knowledge/permission/my',
    method: 'get'
  })
}

// 检查用户对知识库的权限
export function checkPermission(knowledgeBaseId, userId) {
  return request({
    url: '/knowledge/permission/check/' + knowledgeBaseId + '/' + userId,
    method: 'get'
  })
}

// 检查当前用户对知识库的权限
export function checkMyPermission(knowledgeBaseId) {
  return request({
    url: '/knowledge/permission/checkMy/' + knowledgeBaseId,
    method: 'get'
  })
}

// 检查用户是否有指定权限
export function hasPermission(knowledgeBaseId, permissionType) {
  return request({
    url: '/knowledge/permission/hasPermission/' + knowledgeBaseId + '/' + permissionType,
    method: 'get'
  })
}

// 查询知识库权限详细
export function getPermission(id) {
  return request({
    url: '/knowledge/permission/' + id,
    method: 'get'
  })
}

// 新增知识库权限
export function addPermission(data) {
  return request({
    url: '/knowledge/permission',
    method: 'post',
    data: data
  })
}

// 修改知识库权限
export function updatePermission(data) {
  return request({
    url: '/knowledge/permission',
    method: 'put',
    data: data
  })
}

// 删除知识库权限
export function delPermission(ids) {
  return request({
    url: '/knowledge/permission/' + ids,
    method: 'delete'
  })
}

// 批量授权
export function batchGrantPermissions(data) {
  return request({
    url: '/knowledge/permission/batchGrant',
    method: 'post',
    data: data
  })
}

// 导出知识库权限
export function exportPermission(query) {
  return request({
    url: '/knowledge/permission/export',
    method: 'post',
    params: query
  })
}

// 查询知识库列表（用于权限管理页面的下拉选择）
export function listKnowledgeBase(query) {
  return request({
    url: '/knowledge/build/knowledge-base/list',
    method: 'get',
    params: query
  })
}
