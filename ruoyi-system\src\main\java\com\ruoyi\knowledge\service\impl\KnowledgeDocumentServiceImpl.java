package com.ruoyi.knowledge.service.impl;

import java.util.List;
import java.util.ArrayList;
import java.util.Date;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.knowledge.mapper.KnowledgeDocumentMapper;
import com.ruoyi.knowledge.domain.KnowledgeDocument;
import com.ruoyi.knowledge.service.IKnowledgeDocumentService;
import com.ruoyi.knowledge.service.IKnowledgeRagService;

/**
 * 知识库文档Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-10
 */
@Service
public class KnowledgeDocumentServiceImpl implements IKnowledgeDocumentService {

    private static final Logger logger = LoggerFactory.getLogger(KnowledgeDocumentServiceImpl.class);

    @Autowired
    private KnowledgeDocumentMapper knowledgeDocumentMapper;

    @Autowired
    private IKnowledgeRagService knowledgeRagService;

    /**
     * 查询知识库文档
     * 
     * @param id 知识库文档主键
     * @return 知识库文档
     */
    @Override
    public KnowledgeDocument selectKnowledgeDocumentById(Long id) {
        return knowledgeDocumentMapper.selectKnowledgeDocumentById(id);
    }

    /**
     * 查询知识库文档列表
     * 
     * @param knowledgeDocument 知识库文档
     * @return 知识库文档
     */
    @Override
    public List<KnowledgeDocument> selectKnowledgeDocumentList(KnowledgeDocument knowledgeDocument) {
        return knowledgeDocumentMapper.selectKnowledgeDocumentList(knowledgeDocument);
    }

    /**
     * 根据文件夹ID查询文档列表
     * 
     * @param folderId 文件夹ID
     * @return 知识库文档集合
     */
    @Override
    public List<KnowledgeDocument> selectKnowledgeDocumentByFolderId(Long folderId) {
        return knowledgeDocumentMapper.selectKnowledgeDocumentByFolderId(folderId);
    }

    /**
     * 根据知识库ID查询文档列表
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 知识库文档集合
     */
    @Override
    public List<KnowledgeDocument> selectKnowledgeDocumentByKnowledgeBaseId(Long knowledgeBaseId) {
        return knowledgeDocumentMapper.selectKnowledgeDocumentByKnowledgeBaseId(knowledgeBaseId);
    }

    /**
     * 根据关键词搜索文档
     * 
     * @param keyword 关键词
     * @return 知识库文档集合
     */
    @Override
    public List<KnowledgeDocument> searchKnowledgeDocumentByKeyword(String keyword) {
        return knowledgeDocumentMapper.searchKnowledgeDocumentByKeyword(keyword);
    }

    /**
     * 新增知识库文档
     * 
     * @param knowledgeDocument 知识库文档
     * @return 结果
     */
    @Override
    public int insertKnowledgeDocument(KnowledgeDocument knowledgeDocument) {
        knowledgeDocument.setCreateTime(DateUtils.getNowDate());
        knowledgeDocument.setCreateBy(SecurityUtils.getUsername());
        knowledgeDocument.setCreatorId(SecurityUtils.getUserId());
        knowledgeDocument.setCreatorName(SecurityUtils.getUsername());
        return knowledgeDocumentMapper.insertKnowledgeDocument(knowledgeDocument);
    }

    /**
     * 修改知识库文档
     * 
     * @param knowledgeDocument 知识库文档
     * @return 结果
     */
    @Override
    public int updateKnowledgeDocument(KnowledgeDocument knowledgeDocument) {
        knowledgeDocument.setUpdateTime(DateUtils.getNowDate());
        knowledgeDocument.setUpdateBy(SecurityUtils.getUsername());
        return knowledgeDocumentMapper.updateKnowledgeDocument(knowledgeDocument);
    }

    /**
     * 批量删除知识库文档
     * 
     * @param ids 需要删除的知识库文档主键
     * @return 结果
     */
    @Override
    public int deleteKnowledgeDocumentByIds(Long[] ids) {
        return knowledgeDocumentMapper.deleteKnowledgeDocumentByIds(ids);
    }

    /**
     * 删除知识库文档信息
     * 
     * @param id 知识库文档主键
     * @return 结果
     */
    @Override
    public int deleteKnowledgeDocumentById(Long id) {
        return knowledgeDocumentMapper.deleteKnowledgeDocumentById(id);
    }

    /**
     * 上传文档
     * 
     * @param file            上传的文件
     * @param folderId        文件夹ID
     * @param knowledgeBaseId 知识库ID
     * @return 结果
     */
    @Override
    public KnowledgeDocument uploadDocument(MultipartFile file, Long folderId, Long knowledgeBaseId) {
        try {
            // 验证文件
            validateUploadFile(file);

            // 上传文件，使用知识库专用的文件类型验证（包括图片格式）
            String[] allowedExtensions = { "pdf", "docx", "doc", "txt", "md", "xls", "xlsx", "jpg", "jpeg", "png",
                    "gif", "bmp" };

            // 调试信息：打印实际路径
            String uploadPath = RuoYiConfig.getUploadPath();
            System.out.println("=== 文件上传调试信息 ===");
            System.out.println("配置的上传路径: " + uploadPath);
            System.out.println("原始文件名: " + file.getOriginalFilename());
            System.out.println("文件大小: " + file.getSize() + " 字节");

            String fileName = FileUploadUtils.upload(uploadPath, file, allowedExtensions);

            System.out.println("生成的文件路径: " + fileName);
            System.out.println("完整物理路径: " + uploadPath + "/" + fileName.replace("/profile", ""));
            System.out.println("========================");

            // 创建文档记录
            KnowledgeDocument document = new KnowledgeDocument();
            document.setName(file.getOriginalFilename());
            document.setTitle(file.getOriginalFilename());
            document.setFolderId(folderId);
            document.setKnowledgeBaseId(knowledgeBaseId);
            document.setType(getFileType(file.getOriginalFilename()));
            document.setFormat(getFileFormat(file.getOriginalFilename()));
            document.setSize(file.getSize());
            document.setFilePath(fileName);
            document.setFileUrl(fileName);
            document.setStatus("0"); // 正常
            document.setProcessStatus("0"); // 未处理
            document.setIsPublic("0"); // 私有
            document.setViewCount(0L);
            document.setDownloadCount(0L);
            document.setVersion("1.0");

            // 如果是图片文件，添加额外的图片信息处理
            if ("image".equals(document.getType())) {
                processImageDocument(document, file);
            }

            insertKnowledgeDocument(document);
            return document;

        } catch (Exception e) {
            throw new RuntimeException("文件上传失败: " + e.getMessage(), e);
        }
    }

    /**
     * 批量上传文档
     * 
     * @param files           上传的文件列表
     * @param folderId        文件夹ID
     * @param knowledgeBaseId 知识库ID
     * @return 结果
     */
    @Override
    public List<KnowledgeDocument> batchUploadDocuments(MultipartFile[] files, Long folderId, Long knowledgeBaseId) {
        List<KnowledgeDocument> documents = new ArrayList<>();
        for (MultipartFile file : files) {
            if (!file.isEmpty()) {
                KnowledgeDocument document = uploadDocument(file, folderId, knowledgeBaseId);
                documents.add(document);
            }
        }
        return documents;
    }

    /**
     * 处理文档内容
     *
     * @param id 文档ID
     * @return 结果
     */
    @Override
    public boolean processDocument(Long id) {
        KnowledgeDocument document = selectKnowledgeDocumentById(id);
        if (document == null) {
            logger.error("文档不存在: {}", id);
            return false;
        }

        try {
            logger.info("开始处理文档: {} (ID: {})", document.getName(), id);

            // 检查文档是否属于某个知识库
            if (document.getKnowledgeBaseId() != null) {
                // 如果文档属于知识库，进行向量化处理
                logger.info("文档属于知识库 {}，开始向量化处理", document.getKnowledgeBaseId());

                // 调用RAG服务进行向量化
                boolean success = knowledgeRagService.addDocumentToKnowledgeBase(
                        document.getKnowledgeBaseId(), document);

                if (success) {
                    // 向量化成功，更新状态
                    document.setProcessStatus("1"); // 已处理
                    document.setLastAccessTime(new Date());
                    updateKnowledgeDocument(document);
                    logger.info("文档 {} 向量化处理成功", document.getName());
                    return true;
                } else {
                    // 向量化失败，更新状态
                    document.setProcessStatus("2"); // 处理失败
                    updateKnowledgeDocument(document);
                    logger.error("文档 {} 向量化处理失败", document.getName());
                    return false;
                }
            } else {
                // 如果文档不属于知识库，只更新状态
                logger.info("文档不属于任何知识库，仅更新处理状态");
                document.setProcessStatus("1"); // 已处理
                document.setLastAccessTime(new Date());
                updateKnowledgeDocument(document);
                return true;
            }

        } catch (Exception e) {
            logger.error("处理文档失败: {}", e.getMessage(), e);
            // 更新为处理失败状态
            document.setProcessStatus("2"); // 处理失败
            updateKnowledgeDocument(document);
            return false;
        }
    }

    /**
     * 更新文档访问次数
     * 
     * @param id 文档ID
     * @return 结果
     */
    @Override
    public int updateViewCount(Long id) {
        return knowledgeDocumentMapper.updateViewCount(id);
    }

    /**
     * 更新文档下载次数
     * 
     * @param id 文档ID
     * @return 结果
     */
    @Override
    public int updateDownloadCount(Long id) {
        return knowledgeDocumentMapper.updateDownloadCount(id);
    }

    /**
     * 获取文档内容
     * 
     * @param id 文档ID
     * @return 文档内容
     */
    @Override
    public String getDocumentContent(Long id) {
        KnowledgeDocument document = selectKnowledgeDocumentById(id);
        if (document != null) {
            updateViewCount(id);
            return document.getContent();
        }
        return null;
    }

    /**
     * 导出文档
     * 
     * @param ids 文档ID列表
     * @return 导出结果
     */
    @Override
    public String exportDocuments(Long[] ids) {
        // TODO: 实现文档导出功能
        return "导出功能开发中...";
    }

    /**
     * 获取文件类型
     */
    private String getFileType(String fileName) {
        if (StringUtils.isEmpty(fileName)) {
            return "unknown";
        }
        String extension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
        switch (extension) {
            case "md":
                return "markdown";
            case "docx":
            case "doc":
                return "word";
            case "pdf":
                return "pdf";
            case "txt":
                return "text";
            case "xls":
            case "xlsx":
                return "excel";
            case "jpg":
            case "jpeg":
            case "png":
            case "gif":
            case "bmp":
                return "image";
            default:
                return "document";
        }
    }

    /**
     * 获取文件格式
     */
    private String getFileFormat(String fileName) {
        if (StringUtils.isEmpty(fileName)) {
            return "unknown";
        }
        return fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
    }

    /**
     * 验证上传文件
     */
    private void validateUploadFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new RuntimeException("上传文件不能为空");
        }

        String fileName = file.getOriginalFilename();
        if (StringUtils.isEmpty(fileName)) {
            throw new RuntimeException("文件名不能为空");
        }

        // 检查文件名长度
        if (fileName.length() > 100) {
            throw new RuntimeException("文件名长度不能超过100个字符");
        }

        // 检查文件大小 (10MB)
        long maxSize = 10 * 1024 * 1024L;
        if (file.getSize() > maxSize) {
            throw new RuntimeException("文件大小不能超过10MB");
        }

        // 检查文件扩展名
        String extension = getFileFormat(fileName);
        String[] allowedExtensions = { "pdf", "docx", "doc", "txt", "md", "xls", "xlsx", "jpg", "jpeg", "png", "gif",
                "bmp" };
        boolean isAllowed = false;
        for (String ext : allowedExtensions) {
            if (ext.equalsIgnoreCase(extension)) {
                isAllowed = true;
                break;
            }
        }

        if (!isAllowed) {
            throw new RuntimeException("不支持的文件格式，仅支持: " + String.join(", ", allowedExtensions));
        }
    }

    /**
     * 处理图片文档的额外信息
     */
    private void processImageDocument(KnowledgeDocument document, MultipartFile file) {
        try {
            // 获取图片的基本信息
            String contentType = file.getContentType();
            if (contentType != null) {
                document.setSummary("图片文件 - " + contentType + " - 大小: " + formatFileSize(file.getSize()));
            }

            // 设置图片相关的标签
            String format = document.getFormat();
            if (format != null) {
                document.setTags("图片," + format.toUpperCase());
            }

            // 可以在这里添加更多图片处理逻辑，比如：
            // 1. 生成缩略图
            // 2. 提取图片元数据（尺寸、拍摄信息等）
            // 3. 图片格式转换
            // 4. 图片压缩

            // 图片文档的处理状态保持为未处理，等待向量化处理
            // 如果图片属于知识库，将通过OCR进行文本提取和向量化
            document.setProcessStatus("0"); // 未处理，等待向量化

        } catch (Exception e) {
            // 如果图片处理失败，记录日志但不影响上传
            System.err.println("图片处理失败: " + e.getMessage());
        }
    }

    /**
     * 格式化文件大小
     */
    private String formatFileSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.1f KB", size / 1024.0);
        } else {
            return String.format("%.1f MB", size / (1024.0 * 1024.0));
        }
    }
}
