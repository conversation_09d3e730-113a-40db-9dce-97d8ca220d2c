package com.ruoyi.web.controller.system;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.service.SmsService;
import java.util.concurrent.TimeUnit;
import java.util.Map;
import java.util.HashMap;

/**
 * 密码重置控制器
 *
 * <AUTHOR>
 */
@RestController
public class SysPasswordResetController extends BaseController {
    @Autowired
    private ISysUserService userService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private SmsService smsService;

    // 短信验证码缓存前缀
    private static final String SMS_CODE_KEY = "sms_code:";
    // 重置密码token缓存前缀
    private static final String RESET_TOKEN_KEY = "reset_token:";
    // 验证码过期时间（分钟）
    private static final int SMS_CODE_EXPIRE_TIME = 5;
    // 重置token过期时间（分钟）
    private static final int RESET_TOKEN_EXPIRE_TIME = 30;

    /**
     * 发送短信验证码
     */
    @Anonymous
    @PostMapping("/sms/send")
    public AjaxResult sendSmsCode(@RequestBody Map<String, String> params) {
        String username = params.get("username");
        String phonenumber = params.get("phonenumber");

        if (StringUtils.isEmpty(username) || StringUtils.isEmpty(phonenumber)) {
            return AjaxResult.error("用户名和手机号不能为空");
        }

        // 验证用户是否存在且手机号匹配
        SysUser user = userService.selectUserByUserName(username);
        if (user == null) {
            return AjaxResult.error("用户不存在");
        }

        if (!phonenumber.equals(user.getPhonenumber())) {
            return AjaxResult.error("手机号与账户不匹配");
        }

        // 检查是否频繁发送
        String smsKey = SMS_CODE_KEY + phonenumber;
        String existingCode = redisCache.getCacheObject(smsKey);
        if (StringUtils.isNotEmpty(existingCode)) {
            return AjaxResult.error("验证码已发送，请稍后再试");
        }

        // 生成6位数字验证码
        String smsCode = String.valueOf((int) ((Math.random() * 9 + 1) * 100000));

        // 调用短信服务发送验证码
        boolean sendResult = smsService.sendVerificationCode(phonenumber, smsCode);

        if (!sendResult) {
            return AjaxResult.error("短信发送失败，请稍后重试");
        }

        // 短信发送成功后，存储验证码到Redis
        redisCache.setCacheObject(smsKey, smsCode, SMS_CODE_EXPIRE_TIME, TimeUnit.MINUTES);

        logger.info("短信验证码发送成功，手机号：{}", phonenumber);
        return AjaxResult.success("验证码发送成功");
    }

    /**
     * 验证手机号和短信验证码
     */
    @Anonymous
    @PostMapping("/forgot-password/verify-phone")
    public AjaxResult verifyPhone(@RequestBody Map<String, String> params) {
        String username = params.get("username");
        String phonenumber = params.get("phonenumber");
        String smsCode = params.get("smsCode");

        if (StringUtils.isEmpty(username) || StringUtils.isEmpty(phonenumber) || StringUtils.isEmpty(smsCode)) {
            return AjaxResult.error("参数不能为空");
        }

        // 验证用户是否存在且手机号匹配
        SysUser user = userService.selectUserByUserName(username);
        if (user == null) {
            return AjaxResult.error("用户不存在");
        }

        if (!phonenumber.equals(user.getPhonenumber())) {
            return AjaxResult.error("手机号与账户不匹配");
        }

        // 验证短信验证码
        String smsKey = SMS_CODE_KEY + phonenumber;
        String cachedCode = redisCache.getCacheObject(smsKey);

        if (StringUtils.isEmpty(cachedCode)) {
            return AjaxResult.error("验证码已过期，请重新获取");
        }

        if (!smsCode.equals(cachedCode)) {
            return AjaxResult.error("验证码错误");
        }

        // 验证成功，删除验证码
        redisCache.deleteObject(smsKey);

        // 生成重置密码token
        String resetToken = IdUtils.fastUUID();
        String tokenKey = RESET_TOKEN_KEY + resetToken;

        // 存储用户信息到token中
        Map<String, Object> tokenData = new HashMap<>();
        tokenData.put("userId", user.getUserId());
        tokenData.put("username", username);
        tokenData.put("phonenumber", phonenumber);

        redisCache.setCacheObject(tokenKey, tokenData, RESET_TOKEN_EXPIRE_TIME, TimeUnit.MINUTES);

        Map<String, String> result = new HashMap<>();
        result.put("resetToken", resetToken);

        return AjaxResult.success("验证成功", result);
    }

    /**
     * 重置密码
     */
    @Anonymous
    @PostMapping("/forgot-password/reset")
    public AjaxResult resetPassword(@RequestBody Map<String, String> params) {
        String resetToken = params.get("resetToken");
        String newPassword = params.get("newPassword");
        String confirmPassword = params.get("confirmPassword");

        if (StringUtils.isEmpty(resetToken) || StringUtils.isEmpty(newPassword)
                || StringUtils.isEmpty(confirmPassword)) {
            return AjaxResult.error("参数不能为空");
        }

        if (!newPassword.equals(confirmPassword)) {
            return AjaxResult.error("两次输入的密码不一致");
        }

        // 验证重置token
        String tokenKey = RESET_TOKEN_KEY + resetToken;
        Map<String, Object> tokenData = redisCache.getCacheObject(tokenKey);

        if (tokenData == null) {
            return AjaxResult.error("重置链接已过期，请重新验证");
        }

        Long userId = (Long) tokenData.get("userId");

        // 获取用户信息
        SysUser user = userService.selectUserById(userId);
        if (user == null) {
            return AjaxResult.error("用户不存在");
        }

        // 更新密码
        user.setPassword(SecurityUtils.encryptPassword(newPassword));
        user.setUpdateBy("system");

        int result = userService.updateUser(user);

        if (result > 0) {
            // 删除重置token
            redisCache.deleteObject(tokenKey);
            return AjaxResult.success("密码重置成功");
        } else {
            return AjaxResult.error("密码重置失败");
        }
    }
}
