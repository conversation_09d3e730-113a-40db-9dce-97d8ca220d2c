<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.knowledge.mapper.KnowledgeStrategyTemplateMapper">
    
    <resultMap type="KnowledgeStrategyTemplate" id="KnowledgeStrategyTemplateResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="description"    column="description"    />
        <result property="strategyType"    column="strategy_type"    />
        <result property="configJson"    column="config_json"    />
        <result property="isDefault"    column="is_default"    />
        <result property="isEnabled"    column="is_enabled"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="creatorId"    column="creator_id"    />
        <result property="creatorName"    column="creator_name"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectKnowledgeStrategyTemplateVo">
        select id, name, description, strategy_type, config_json, is_default, is_enabled, sort_order, creator_id, creator_name, create_by, create_time, update_by, update_time, remark from knowledge_strategy_template
    </sql>

    <select id="selectKnowledgeStrategyTemplateList" parameterType="KnowledgeStrategyTemplate" resultMap="KnowledgeStrategyTemplateResult">
        <include refid="selectKnowledgeStrategyTemplateVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="strategyType != null  and strategyType != ''"> and strategy_type = #{strategyType}</if>
            <if test="isDefault != null  and isDefault != ''"> and is_default = #{isDefault}</if>
            <if test="isEnabled != null  and isEnabled != ''"> and is_enabled = #{isEnabled}</if>
            <if test="creatorId != null "> and creator_id = #{creatorId}</if>
        </where>
        order by sort_order asc, create_time desc
    </select>
    
    <select id="selectKnowledgeStrategyTemplateById" parameterType="Long" resultMap="KnowledgeStrategyTemplateResult">
        <include refid="selectKnowledgeStrategyTemplateVo"/>
        where id = #{id}
    </select>

    <select id="selectKnowledgeStrategyTemplateByType" parameterType="String" resultMap="KnowledgeStrategyTemplateResult">
        <include refid="selectKnowledgeStrategyTemplateVo"/>
        where strategy_type = #{strategyType} and is_enabled = '1'
        order by sort_order asc, create_time desc
    </select>

    <select id="selectDefaultKnowledgeStrategyTemplateByType" parameterType="String" resultMap="KnowledgeStrategyTemplateResult">
        <include refid="selectKnowledgeStrategyTemplateVo"/>
        where strategy_type = #{strategyType} and is_default = '1' and is_enabled = '1'
        order by sort_order asc, create_time desc
        limit 1
    </select>

    <select id="selectEnabledKnowledgeStrategyTemplates" resultMap="KnowledgeStrategyTemplateResult">
        <include refid="selectKnowledgeStrategyTemplateVo"/>
        where is_enabled = '1'
        order by strategy_type asc, sort_order asc, create_time desc
    </select>
        
    <insert id="insertKnowledgeStrategyTemplate" parameterType="KnowledgeStrategyTemplate" useGeneratedKeys="true" keyProperty="id">
        insert into knowledge_strategy_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="description != null">description,</if>
            <if test="strategyType != null and strategyType != ''">strategy_type,</if>
            <if test="configJson != null">config_json,</if>
            <if test="isDefault != null">is_default,</if>
            <if test="isEnabled != null">is_enabled,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="creatorId != null">creator_id,</if>
            <if test="creatorName != null">creator_name,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="description != null">#{description},</if>
            <if test="strategyType != null and strategyType != ''">#{strategyType},</if>
            <if test="configJson != null">#{configJson},</if>
            <if test="isDefault != null">#{isDefault},</if>
            <if test="isEnabled != null">#{isEnabled},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="creatorId != null">#{creatorId},</if>
            <if test="creatorName != null">#{creatorName},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateKnowledgeStrategyTemplate" parameterType="KnowledgeStrategyTemplate">
        update knowledge_strategy_template
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="description != null">description = #{description},</if>
            <if test="strategyType != null and strategyType != ''">strategy_type = #{strategyType},</if>
            <if test="configJson != null">config_json = #{configJson},</if>
            <if test="isDefault != null">is_default = #{isDefault},</if>
            <if test="isEnabled != null">is_enabled = #{isEnabled},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="creatorId != null">creator_id = #{creatorId},</if>
            <if test="creatorName != null">creator_name = #{creatorName},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteKnowledgeStrategyTemplateById" parameterType="Long">
        delete from knowledge_strategy_template where id = #{id}
    </delete>

    <delete id="deleteKnowledgeStrategyTemplateByIds" parameterType="String">
        delete from knowledge_strategy_template where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
