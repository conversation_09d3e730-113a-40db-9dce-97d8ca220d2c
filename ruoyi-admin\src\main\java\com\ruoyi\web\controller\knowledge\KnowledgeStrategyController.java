package com.ruoyi.web.controller.knowledge;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.knowledge.domain.KnowledgeStrategyTemplate;
import com.ruoyi.knowledge.service.IKnowledgeStrategyTemplateService;
import com.ruoyi.knowledge.enums.StrategyType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Arrays;
import java.util.ArrayList;
import java.util.stream.Collectors;

/**
 * 知识库策略管理Controller
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
@RestController
@RequestMapping("/knowledge/strategy")
public class KnowledgeStrategyController extends BaseController
{
    @Autowired
    private IKnowledgeStrategyTemplateService knowledgeStrategyTemplateService;

    /**
     * 查询知识库策略模板列表
     */
    @PreAuthorize("@ss.hasPermi('knowledge:strategy:list')")
    @GetMapping("/template/list")
    public TableDataInfo list(KnowledgeStrategyTemplate knowledgeStrategyTemplate)
    {
        startPage();
        List<KnowledgeStrategyTemplate> list = knowledgeStrategyTemplateService.selectKnowledgeStrategyTemplateList(knowledgeStrategyTemplate);
        return getDataTable(list);
    }

    /**
     * 导出知识库策略模板列表
     */
    @PreAuthorize("@ss.hasPermi('knowledge:strategy:export')")
    @Log(title = "知识库策略模板", businessType = BusinessType.EXPORT)
    @PostMapping("/template/export")
    public void export(HttpServletResponse response, KnowledgeStrategyTemplate knowledgeStrategyTemplate)
    {
        List<KnowledgeStrategyTemplate> list = knowledgeStrategyTemplateService.selectKnowledgeStrategyTemplateList(knowledgeStrategyTemplate);
        ExcelUtil<KnowledgeStrategyTemplate> util = new ExcelUtil<KnowledgeStrategyTemplate>(KnowledgeStrategyTemplate.class);
        util.exportExcel(response, list, "知识库策略模板数据");
    }

    /**
     * 获取知识库策略模板详细信息
     */
    @PreAuthorize("@ss.hasPermi('knowledge:strategy:query')")
    @GetMapping(value = "/template/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(knowledgeStrategyTemplateService.selectKnowledgeStrategyTemplateById(id));
    }

    /**
     * 新增知识库策略模板
     */
    @PreAuthorize("@ss.hasPermi('knowledge:strategy:add')")
    @Log(title = "知识库策略模板", businessType = BusinessType.INSERT)
    @PostMapping("/template")
    public AjaxResult add(@RequestBody KnowledgeStrategyTemplate knowledgeStrategyTemplate)
    {
        return toAjax(knowledgeStrategyTemplateService.insertKnowledgeStrategyTemplate(knowledgeStrategyTemplate));
    }

    /**
     * 修改知识库策略模板
     */
    @PreAuthorize("@ss.hasPermi('knowledge:strategy:edit')")
    @Log(title = "知识库策略模板", businessType = BusinessType.UPDATE)
    @PutMapping("/template")
    public AjaxResult edit(@RequestBody KnowledgeStrategyTemplate knowledgeStrategyTemplate)
    {
        return toAjax(knowledgeStrategyTemplateService.updateKnowledgeStrategyTemplate(knowledgeStrategyTemplate));
    }

    /**
     * 删除知识库策略模板
     */
    @PreAuthorize("@ss.hasPermi('knowledge:strategy:remove')")
    @Log(title = "知识库策略模板", businessType = BusinessType.DELETE)
	@DeleteMapping("/template/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(knowledgeStrategyTemplateService.deleteKnowledgeStrategyTemplateByIds(ids));
    }

    /**
     * 根据策略类型查询策略模板
     */
    @GetMapping("/template/type/{strategyType}")
    public AjaxResult getTemplatesByType(@PathVariable("strategyType") String strategyType)
    {
        List<KnowledgeStrategyTemplate> templates = knowledgeStrategyTemplateService.selectKnowledgeStrategyTemplateByType(strategyType);
        return success(templates);
    }

    /**
     * 从现有的策略配置表转换数据
     */
    private List<KnowledgeStrategyTemplate> convertFromExistingStrategyConfig(String strategyType) {
        List<KnowledgeStrategyTemplate> templates = new ArrayList<>();

        // 根据策略类型创建模拟的策略模板数据
        switch (strategyType.toUpperCase()) {
            case "INITIALIZATION":
                templates.add(createMockTemplate(1L, "默认初始化策略", "知识库的默认初始化策略", "INITIALIZATION"));
                break;
            case "SEGMENTATION":
                templates.add(createMockTemplate(2L, "固定长度分割策略", "按照固定字符数量分割文档", "SEGMENTATION"));
                templates.add(createMockTemplate(3L, "语义分割策略", "基于语义边界分割文档", "SEGMENTATION"));
                templates.add(createMockTemplate(4L, "句子分割策略", "按照句子边界分割文档", "SEGMENTATION"));
                break;
            case "TAGGING":
                templates.add(createMockTemplate(5L, "关键词提取标签策略", "基于词频和TF-IDF提取关键词", "TAGGING"));
                templates.add(createMockTemplate(6L, "主题分类标签策略", "基于主题词典进行文档分类", "TAGGING"));
                break;
            case "SUMMARIZATION":
                templates.add(createMockTemplate(7L, "抽取式摘要策略", "从原文中抽取关键句子形成摘要", "SUMMARIZATION"));
                break;
            case "ASSOCIATION":
                templates.add(createMockTemplate(8L, "相似度关联策略", "基于向量相似度发现相关文档", "ASSOCIATION"));
                break;
        }

        return templates;
    }

    private KnowledgeStrategyTemplate createMockTemplate(Long id, String name, String description, String strategyType) {
        KnowledgeStrategyTemplate template = new KnowledgeStrategyTemplate();
        template.setId(id);
        template.setName(name);
        template.setDescription(description);
        template.setStrategyType(strategyType);
        template.setIsDefault("1");
        template.setIsEnabled("1");
        template.setSortOrder(1);
        return template;
    }

    /**
     * 获取所有启用的策略模板
     */
    @PreAuthorize("@ss.hasPermi('knowledge:strategy:query')")
    @GetMapping("/template/enabled")
    public AjaxResult getEnabledTemplates()
    {
        List<KnowledgeStrategyTemplate> templates = knowledgeStrategyTemplateService.selectEnabledKnowledgeStrategyTemplates();
        return success(templates);
    }

    /**
     * 获取策略类型列表
     */
    @GetMapping("/types")
    public AjaxResult getStrategyTypes()
    {
        List<StrategyTypeInfo> types = Arrays.stream(StrategyType.values())
                .map(type -> new StrategyTypeInfo(type.getCode(), type.getName(), type.getDescription()))
                .collect(Collectors.toList());
        return success(types);
    }

    /**
     * 验证策略配置
     */
    @PreAuthorize("@ss.hasPermi('knowledge:strategy:query')")
    @PostMapping("/validate")
    public AjaxResult validateConfig(@RequestBody ValidateConfigRequest request)
    {
        boolean isValid = knowledgeStrategyTemplateService.validateStrategyConfig(request.getStrategyType(), request.getConfigJson());
        return success(isValid);
    }

    /**
     * 策略类型信息
     */
    public static class StrategyTypeInfo {
        private String code;
        private String name;
        private String description;

        public StrategyTypeInfo(String code, String name, String description) {
            this.code = code;
            this.name = name;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }
    }

    /**
     * 验证配置请求
     */
    public static class ValidateConfigRequest {
        private String strategyType;
        private String configJson;

        public String getStrategyType() {
            return strategyType;
        }

        public void setStrategyType(String strategyType) {
            this.strategyType = strategyType;
        }

        public String getConfigJson() {
            return configJson;
        }

        public void setConfigJson(String configJson) {
            this.configJson = configJson;
        }
    }
}
