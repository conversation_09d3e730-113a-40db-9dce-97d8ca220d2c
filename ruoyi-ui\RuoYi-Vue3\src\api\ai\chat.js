import request from '@/utils/request'
import axios from 'axios'
import { getToken } from '@/utils/auth'
import { mockApi, isDevelopment } from './mock'

// 创建专门用于AI请求的axios实例，使用更长的超时时间
const aiRequest = axios.create({
  baseURL: import.meta.env.VITE_APP_BASE_API,
  timeout: 180000, // 3分钟超时，适应AI响应时间
  headers: {
    'Content-Type': 'application/json;charset=utf-8'
  }
})

// AI请求拦截器
aiRequest.interceptors.request.use(config => {
  if (getToken()) {
    config.headers['Authorization'] = 'Bearer ' + getToken()
  }
  return config
}, error => {
  console.log(error)
  return Promise.reject(error)
})

// AI响应拦截器
aiRequest.interceptors.response.use(res => {
  const code = res.data.code || 200
  if (code === 200) {
    return res.data
  } else {
    return Promise.reject(new Error(res.data.msg || '请求失败'))
  }
}, error => {
  console.log('AI请求错误:', error)
  let message = error.message
  if (message.includes("timeout")) {
    message = "AI响应超时，请稍后重试"
  } else if (message.includes("Network Error")) {
    message = "网络连接异常"
  }
  return Promise.reject(new Error(message))
})

// 使用真实API进行前后端联调
const useApi = {
  // AI消息发送使用专用的长超时请求实例
  sendMessage: (data) => aiRequest({ url: '/ai/chat/send', method: 'post', data }),
  getChatHistory: (params) => request({ url: '/ai/chat/history', method: 'get', params }),
  createChatSession: (data) => request({ url: '/ai/chat/session', method: 'post', data }),
  getChatSessions: (params) => request({ url: '/ai/chat/sessions', method: 'get', params }),
  deleteChatSession: (sessionId) => request({ url: '/ai/chat/session/' + sessionId, method: 'delete' }),
  clearChatHistory: (sessionId) => request({ url: '/ai/chat/clear/' + sessionId, method: 'delete' }),
  getAiModels: () => request({ url: '/ai/chat/models', method: 'get' })
}

// 发送消息到AI
export function sendMessage(data) {
  return useApi.sendMessage(data)
}

// 获取聊天历史记录
export function getChatHistory(params) {
  return useApi.getChatHistory(params)
}

// 创建新的聊天会话
export function createChatSession(data) {
  return useApi.createChatSession(data)
}

// 获取聊天会话列表
export function getChatSessions(params) {
  return useApi.getChatSessions(params)
}

// 删除聊天会话
export function deleteChatSession(sessionId) {
  return useApi.deleteChatSession(sessionId)
}

// 清空聊天记录
export function clearChatHistory(sessionId) {
  return useApi.clearChatHistory(sessionId)
}

// 获取AI模型列表
export function getAiModels() {
  return useApi.getAiModels()
}








