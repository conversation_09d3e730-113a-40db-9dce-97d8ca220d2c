package com.ruoyi.knowledge.enums;

/**
 * 策略执行状态枚举
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
public enum ExecutionStatus {
    
    /**
     * 待执行
     */
    PENDING("PENDING", "待执行"),
    
    /**
     * 执行中
     */
    RUNNING("RUNNING", "执行中"),
    
    /**
     * 执行成功
     */
    SUCCESS("SUCCESS", "执行成功"),
    
    /**
     * 执行失败
     */
    FAILED("FAILED", "执行失败");

    private final String code;
    private final String name;

    ExecutionStatus(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    /**
     * 根据代码获取执行状态
     */
    public static ExecutionStatus fromCode(String code) {
        for (ExecutionStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown execution status code: " + code);
    }

    /**
     * 检查代码是否有效
     */
    public static boolean isValidCode(String code) {
        for (ExecutionStatus status : values()) {
            if (status.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }
}
