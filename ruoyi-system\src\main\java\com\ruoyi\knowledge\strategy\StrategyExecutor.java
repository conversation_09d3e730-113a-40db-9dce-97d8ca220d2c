package com.ruoyi.knowledge.strategy;

import com.ruoyi.knowledge.domain.KnowledgeBaseStrategyConfig;
import com.ruoyi.knowledge.domain.KnowledgeStrategyExecutionLog;
import com.ruoyi.knowledge.enums.ExecutionStatus;
import com.ruoyi.knowledge.enums.StrategyType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * 策略执行器
 * 负责执行知识库策略，支持串行和并行执行
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
@Component
public class StrategyExecutor {
    
    private static final Logger logger = LoggerFactory.getLogger(StrategyExecutor.class);
    
    @Autowired
    private StrategyFactory strategyFactory;
    
    /** 线程池，用于并行执行策略 */
    private final ExecutorService executorService = Executors.newFixedThreadPool(10);
    
    /**
     * 执行单个策略
     * 
     * @param strategyConfig 策略配置
     * @param input 输入数据
     * @return 执行结果
     */
    @SuppressWarnings("unchecked")
    public StrategyExecutionResult executeSingleStrategy(KnowledgeBaseStrategyConfig strategyConfig, Object input) {
        StrategyExecutionResult result = new StrategyExecutionResult();
        result.setStrategyConfig(strategyConfig);
        result.setStartTime(new Date());
        result.setStatus(ExecutionStatus.RUNNING);
        
        try {
            // 获取策略实例
            StrategyType strategyType = StrategyType.fromCode(strategyConfig.getStrategyType());
            KnowledgeStrategy strategy = strategyFactory.getStrategy(strategyType, 
                    strategyConfig.getStrategyTemplate().getName());
            
            if (strategy == null) {
                // 尝试获取默认策略
                strategy = strategyFactory.getDefaultStrategy(strategyType);
            }
            
            if (strategy == null) {
                throw new IllegalStateException("找不到策略实现: " + strategyType + " - " + 
                        strategyConfig.getStrategyTemplate().getName());
            }
            
            logger.info("开始执行策略: {} - {}", strategyType, strategy.getStrategyName());
            
            // 执行策略
            Object output = strategy.execute(input, strategyConfig.getConfigJson());
            
            result.setOutput(output);
            result.setStatus(ExecutionStatus.SUCCESS);
            result.setMessage("策略执行成功");
            
            logger.info("策略执行成功: {} - {}", strategyType, strategy.getStrategyName());
            
        } catch (Exception e) {
            result.setStatus(ExecutionStatus.FAILED);
            result.setMessage("策略执行失败: " + e.getMessage());
            result.setErrorMessage(e.getMessage());
            logger.error("策略执行失败: {}", strategyConfig.getStrategyType(), e);
        } finally {
            result.setEndTime(new Date());
            if (result.getStartTime() != null && result.getEndTime() != null) {
                result.setDurationMs(result.getEndTime().getTime() - result.getStartTime().getTime());
            }
        }
        
        return result;
    }
    
    /**
     * 执行多个策略（串行）
     * 
     * @param strategyConfigs 策略配置列表
     * @param input 输入数据
     * @return 执行结果列表
     */
    public List<StrategyExecutionResult> executeStrategiesSequentially(
            List<KnowledgeBaseStrategyConfig> strategyConfigs, Object input) {
        
        List<StrategyExecutionResult> results = new ArrayList<>();
        Object currentInput = input;
        
        // 按执行顺序排序
        List<KnowledgeBaseStrategyConfig> sortedConfigs = strategyConfigs.stream()
                .sorted(Comparator.comparingInt(KnowledgeBaseStrategyConfig::getExecutionOrder))
                .collect(Collectors.toList());
        
        for (KnowledgeBaseStrategyConfig config : sortedConfigs) {
            if (!"1".equals(config.getIsEnabled())) {
                logger.debug("跳过禁用的策略: {}", config.getStrategyType());
                continue;
            }
            
            StrategyExecutionResult result = executeSingleStrategy(config, currentInput);
            results.add(result);
            
            // 如果策略执行失败，根据配置决定是否继续
            if (result.getStatus() == ExecutionStatus.FAILED) {
                logger.warn("策略执行失败，停止后续策略执行: {}", config.getStrategyType());
                break;
            }
            
            // 将当前策略的输出作为下一个策略的输入
            if (result.getOutput() != null) {
                currentInput = result.getOutput();
            }
        }
        
        return results;
    }
    
    /**
     * 执行多个策略（并行）
     * 
     * @param strategyConfigs 策略配置列表
     * @param input 输入数据
     * @return 执行结果列表
     */
    public List<StrategyExecutionResult> executeStrategiesParallel(
            List<KnowledgeBaseStrategyConfig> strategyConfigs, Object input) {
        
        // 过滤启用的策略
        List<KnowledgeBaseStrategyConfig> enabledConfigs = strategyConfigs.stream()
                .filter(config -> "1".equals(config.getIsEnabled()))
                .collect(Collectors.toList());
        
        // 创建并行执行任务
        List<CompletableFuture<StrategyExecutionResult>> futures = enabledConfigs.stream()
                .map(config -> CompletableFuture.supplyAsync(() -> executeSingleStrategy(config, input), executorService))
                .collect(Collectors.toList());
        
        // 等待所有任务完成
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        
        try {
            allFutures.get(); // 等待所有任务完成
        } catch (Exception e) {
            logger.error("并行执行策略时发生异常", e);
        }
        
        // 收集结果
        List<StrategyExecutionResult> results = new ArrayList<>();
        for (CompletableFuture<StrategyExecutionResult> future : futures) {
            try {
                results.add(future.get());
            } catch (Exception e) {
                logger.error("获取策略执行结果时发生异常", e);
            }
        }
        
        return results;
    }
    
    /**
     * 策略执行结果
     */
    public static class StrategyExecutionResult {
        private KnowledgeBaseStrategyConfig strategyConfig;
        private ExecutionStatus status;
        private Date startTime;
        private Date endTime;
        private Long durationMs;
        private Object output;
        private String message;
        private String errorMessage;
        
        // Getters and Setters
        public KnowledgeBaseStrategyConfig getStrategyConfig() {
            return strategyConfig;
        }
        
        public void setStrategyConfig(KnowledgeBaseStrategyConfig strategyConfig) {
            this.strategyConfig = strategyConfig;
        }
        
        public ExecutionStatus getStatus() {
            return status;
        }
        
        public void setStatus(ExecutionStatus status) {
            this.status = status;
        }
        
        public Date getStartTime() {
            return startTime;
        }
        
        public void setStartTime(Date startTime) {
            this.startTime = startTime;
        }
        
        public Date getEndTime() {
            return endTime;
        }
        
        public void setEndTime(Date endTime) {
            this.endTime = endTime;
        }
        
        public Long getDurationMs() {
            return durationMs;
        }
        
        public void setDurationMs(Long durationMs) {
            this.durationMs = durationMs;
        }
        
        public Object getOutput() {
            return output;
        }
        
        public void setOutput(Object output) {
            this.output = output;
        }
        
        public String getMessage() {
            return message;
        }
        
        public void setMessage(String message) {
            this.message = message;
        }
        
        public String getErrorMessage() {
            return errorMessage;
        }
        
        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }
    }
}
