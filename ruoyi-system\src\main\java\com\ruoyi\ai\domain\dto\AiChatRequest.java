package com.ruoyi.ai.domain.dto;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * AI聊天请求DTO
 * 
 * <AUTHOR>
 * @date 2024-07-09
 */
public class AiChatRequest {
    /** 会话ID */
    @NotBlank(message = "会话ID不能为空")
    private String sessionId;

    /** 消息内容 */
    @NotBlank(message = "消息内容不能为空")
    @Size(max = 4000, message = "消息内容不能超过4000个字符")
    private String message;

    /** AI模型 */
    private String model = "qwen-plus";

    /** 是否流式输出 */
    private Boolean stream = false;

    /** 知识库ID（可选，用于RAG增强） */
    private Long knowledgeBaseId;

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public Boolean getStream() {
        return stream;
    }

    public void setStream(Boolean stream) {
        this.stream = stream;
    }

    public Long getKnowledgeBaseId() {
        return knowledgeBaseId;
    }

    public void setKnowledgeBaseId(Long knowledgeBaseId) {
        this.knowledgeBaseId = knowledgeBaseId;
    }

    @Override
    public String toString() {
        return "AiChatRequest{" +
                "sessionId='" + sessionId + '\'' +
                ", message='" + message + '\'' +
                ", model='" + model + '\'' +
                ", stream=" + stream +
                ", knowledgeBaseId=" + knowledgeBaseId +
                '}';
    }
}
