package com.ruoyi.web.controller.ai;

import com.ruoyi.ai.domain.AiChatMessage;
import com.ruoyi.ai.domain.AiChatSession;
import com.ruoyi.ai.domain.dto.AiChatRequest;
import com.ruoyi.ai.domain.dto.AiChatResponse;
import com.ruoyi.ai.domain.dto.AiSessionRequest;
import com.ruoyi.ai.service.IAiChatService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * AI聊天Controller
 * 
 * <AUTHOR>
 * @date 2024-07-09
 */
@RestController
@RequestMapping("/ai/chat")
public class AiChatController extends BaseController {

    @Autowired
    private IAiChatService aiChatService;

    /**
     * 创建会话
     */
    @PreAuthorize("@ss.hasPermi('ai:chat:session')")
    @Log(title = "AI聊天", businessType = BusinessType.INSERT)
    @PostMapping("/session")
    public AjaxResult createSession(@Valid @RequestBody AiSessionRequest request) {
        try {
            Long userId = SecurityUtils.getUserId();
            String userName = SecurityUtils.getUsername();

            AiChatSession session = aiChatService.createSession(request, userId, userName);
            return success(session);
        } catch (Exception e) {
            logger.error("创建会话失败: {}", e.getMessage(), e);
            return error("创建会话失败: " + e.getMessage());
        }
    }

    /**
     * 获取会话列表
     */
    @PreAuthorize("@ss.hasPermi('ai:chat:view')")
    @GetMapping("/sessions")
    public AjaxResult getSessions() {
        try {
            Long userId = SecurityUtils.getUserId();
            List<AiChatSession> sessions = aiChatService.getUserSessions(userId);
            return success(sessions);
        } catch (Exception e) {
            logger.error("获取会话列表失败: {}", e.getMessage(), e);
            return error("获取会话列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取聊天历史
     */
    @PreAuthorize("@ss.hasPermi('ai:chat:history')")
    @GetMapping("/history")
    public AjaxResult getHistory(@RequestParam String sessionId) {
        try {
            Long userId = SecurityUtils.getUserId();
            List<AiChatMessage> messages = aiChatService.getSessionHistory(sessionId, userId);
            return success(messages);
        } catch (Exception e) {
            logger.error("获取聊天历史失败: {}", e.getMessage(), e);
            return error("获取聊天历史失败: " + e.getMessage());
        }
    }

    /**
     * 发送消息（同步）
     */
    @PreAuthorize("@ss.hasPermi('ai:chat:send')")
    @Log(title = "AI聊天", businessType = BusinessType.OTHER)
    @PostMapping("/send")
    public AjaxResult sendMessage(@Valid @RequestBody AiChatRequest request) {
        try {
            logger.info("收到AI聊天请求: sessionId={}, message={}, knowledgeBaseId={}",
                    request.getSessionId(), request.getMessage(), request.getKnowledgeBaseId());

            Long userId = SecurityUtils.getUserId();
            String userName = SecurityUtils.getUsername();

            AiChatResponse response = aiChatService.sendMessage(request, userId, userName);
            return success(response);
        } catch (Exception e) {
            logger.error("发送消息失败: {}", e.getMessage(), e);
            return error("发送消息失败: " + e.getMessage());
        }
    }

    /**
     * 删除会话
     */
    @PreAuthorize("@ss.hasPermi('ai:chat:session')")
    @Log(title = "AI聊天", businessType = BusinessType.DELETE)
    @DeleteMapping("/session/{sessionId}")
    public AjaxResult deleteSession(@PathVariable String sessionId) {
        try {
            Long userId = SecurityUtils.getUserId();
            boolean result = aiChatService.deleteSession(sessionId, userId);
            return result ? success() : error("删除会话失败");
        } catch (Exception e) {
            logger.error("删除会话失败: {}", e.getMessage(), e);
            return error("删除会话失败: " + e.getMessage());
        }
    }

    /**
     * 清空聊天记录
     */
    @PreAuthorize("@ss.hasPermi('ai:chat:session')")
    @Log(title = "AI聊天", businessType = BusinessType.DELETE)
    @DeleteMapping("/clear/{sessionId}")
    public AjaxResult clearHistory(@PathVariable String sessionId) {
        try {
            Long userId = SecurityUtils.getUserId();
            boolean result = aiChatService.clearSessionMessages(sessionId, userId);
            return result ? success() : error("清空聊天记录失败");
        } catch (Exception e) {
            logger.error("清空聊天记录失败: {}", e.getMessage(), e);
            return error("清空聊天记录失败: " + e.getMessage());
        }
    }

    /**
     * 获取AI模型列表
     */
    @PreAuthorize("@ss.hasPermi('ai:chat:view')")
    @GetMapping("/models")
    public AjaxResult getModels() {
        try {
            List<String> models = aiChatService.getAvailableModels();
            return success(models.stream().map(model -> {
                Map<String, String> modelInfo = new HashMap<>();
                modelInfo.put("label", model);
                modelInfo.put("value", model);
                return modelInfo;
            }).toArray());
        } catch (Exception e) {
            logger.error("获取模型列表失败: {}", e.getMessage(), e);
            return error("获取模型列表失败: " + e.getMessage());
        }
    }
}
