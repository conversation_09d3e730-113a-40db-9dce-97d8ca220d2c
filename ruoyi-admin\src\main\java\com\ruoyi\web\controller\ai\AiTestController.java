package com.ruoyi.web.controller.ai;

import com.ruoyi.ai.config.AiConfig;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import dev.langchain4j.memory.ChatMemory;
import dev.langchain4j.model.chat.ChatLanguageModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * AI测试Controller
 *
 * <AUTHOR>
 * @date 2024-07-09
 */
@Anonymous
@RestController
@RequestMapping("/ai/test")
public class AiTestController extends BaseController {

    @Autowired
    private ChatLanguageModel chatLanguageModel;

    @Autowired
    private AiConfig aiConfig;

    @Value("${langchain4j.open-ai.chat-model.api-key:}")
    private String apiKey;

    @Value("${langchain4j.open-ai.chat-model.base-url:}")
    private String baseUrl;

    @Value("${langchain4j.open-ai.chat-model.model-name:qwen-plus}")
    private String modelName;

    /**
     * 测试AI连接
     */
    @GetMapping("/connection")
    public AjaxResult testConnection() {
        try {
            // 检查配置
            if (apiKey == null || apiKey.trim().isEmpty()) {
                return error("API密钥未设置，请检查环境变量API_KEY");
            }

            if (apiKey.startsWith("${") && apiKey.endsWith("}")) {
                return error("API密钥未正确配置，请检查环境变量API_KEY");
            }

            // 测试简单对话
            String testMessage = "你好，请简单介绍一下自己";
            String response = chatLanguageModel.generate(testMessage);

            Map<String, Object> result = new HashMap<>();
            result.put("apiKey", apiKey.substring(0, Math.min(10, apiKey.length())) + "...");
            result.put("baseUrl", baseUrl);
            result.put("modelName", modelName);
            result.put("testMessage", testMessage);
            result.put("response", response);

            return AjaxResult.success("AI连接测试成功", result);

        } catch (Exception e) {
            logger.error("AI连接测试失败: {}", e.getMessage(), e);
            return error("AI连接测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试简单聊天
     */
    @PostMapping("/chat")
    public AjaxResult testChat(@RequestBody Map<String, String> request) {
        String message = request.get("message");
        try {
            if (message == null || message.trim().isEmpty()) {
                return error("消息内容不能为空");
            }

            String response = chatLanguageModel.generate(message);

            Map<String, Object> result = new HashMap<>();
            result.put("userMessage", message);
            result.put("aiResponse", response);
            result.put("timestamp", System.currentTimeMillis());

            return AjaxResult.success("聊天测试成功", result);

        } catch (Exception e) {
            logger.error("聊天测试失败: {}", e.getMessage(), e);
            return error("聊天测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试会话记忆
     */
    @PostMapping("/memory")
    public AjaxResult testMemory(@RequestBody Map<String, String> request) {
        String sessionId = request.get("sessionId");
        String message = request.get("message");
        try {
            if (sessionId == null || sessionId.trim().isEmpty()) {
                return error("会话ID不能为空");
            }

            if (message == null || message.trim().isEmpty()) {
                return error("消息内容不能为空");
            }

            // 获取或创建会话记忆
            ChatMemory chatMemory = aiConfig.getOrCreateChatMemory(sessionId);

            // 创建AI助手
            AiConfig.AiAssistant assistant = aiConfig.createAiAssistant(chatMemory);

            // 发送消息
            String response = assistant.chat(message);

            Map<String, Object> result = new HashMap<>();
            result.put("sessionId", sessionId);
            result.put("userMessage", message);
            result.put("aiResponse", response);
            result.put("memorySize", chatMemory.messages().size());
            result.put("timestamp", System.currentTimeMillis());

            return AjaxResult.success("会话记忆测试成功", result);

        } catch (Exception e) {
            logger.error("会话记忆测试失败: {}", e.getMessage(), e);
            return error("会话记忆测试失败: " + e.getMessage());
        }
    }

    /**
     * 清除会话记忆
     */
    @DeleteMapping("/memory/{sessionId}")
    public AjaxResult clearMemory(@PathVariable String sessionId) {
        try {
            aiConfig.clearChatMemory(sessionId);
            return success("会话记忆清除成功");
        } catch (Exception e) {
            logger.error("清除会话记忆失败: {}", e.getMessage(), e);
            return error("清除会话记忆失败: " + e.getMessage());
        }
    }

    /**
     * 获取系统信息
     */
    @GetMapping("/system")
    public AjaxResult getSystemInfo() {
        try {
            Map<String, Object> systemInfo = new HashMap<>();
            systemInfo.put("javaVersion", System.getProperty("java.version"));
            systemInfo.put("osName", System.getProperty("os.name"));
            systemInfo.put("osVersion", System.getProperty("os.version"));
            systemInfo.put("maxMemory", Runtime.getRuntime().maxMemory() / 1024 / 1024 + " MB");
            systemInfo.put("totalMemory", Runtime.getRuntime().totalMemory() / 1024 / 1024 + " MB");
            systemInfo.put("freeMemory", Runtime.getRuntime().freeMemory() / 1024 / 1024 + " MB");
            systemInfo.put("availableProcessors", Runtime.getRuntime().availableProcessors());

            Map<String, Object> aiConfig = new HashMap<>();
            aiConfig.put("baseUrl", baseUrl);
            aiConfig.put("modelName", modelName);
            aiConfig.put("apiKeyConfigured", apiKey != null && !apiKey.trim().isEmpty() && !apiKey.startsWith("${"));

            Map<String, Object> result = new HashMap<>();
            result.put("system", systemInfo);
            result.put("aiConfig", aiConfig);
            result.put("timestamp", System.currentTimeMillis());

            return AjaxResult.success("系统信息获取成功", result);

        } catch (Exception e) {
            logger.error("获取系统信息失败: {}", e.getMessage(), e);
            return error("获取系统信息失败: " + e.getMessage());
        }
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public AjaxResult healthCheck() {
        try {
            Map<String, Object> health = new HashMap<>();
            health.put("status", "UP");
            health.put("timestamp", System.currentTimeMillis());

            // 检查API密钥配置
            boolean apiKeyConfigured = apiKey != null && !apiKey.trim().isEmpty() && !apiKey.startsWith("${");
            health.put("apiKeyConfigured", apiKeyConfigured);

            if (!apiKeyConfigured) {
                health.put("status", "DOWN");
                health.put("error", "API密钥未正确配置");
            }

            return AjaxResult.success("健康检查完成", health);

        } catch (Exception e) {
            logger.error("健康检查失败: {}", e.getMessage(), e);
            return error("健康检查失败: " + e.getMessage());
        }
    }

    /**
     * 简化的AI测试接口（不使用流式）
     */
    @PostMapping("/simple-ai")
    public AjaxResult simpleAiTest(@RequestBody Map<String, String> request) {
        String message = request.getOrDefault("message", "你好");
        try {
            logger.info("开始简化AI测试，消息: {}", message);

            // 直接使用同步AI调用
            String response = chatLanguageModel.generate(message);
            logger.info("AI响应成功，长度: {}", response.length());

            Map<String, Object> result = new HashMap<>();
            result.put("message", message);
            result.put("response", response);
            result.put("timestamp", System.currentTimeMillis());

            return AjaxResult.success("简化AI测试成功", result);

        } catch (Exception e) {
            logger.error("简化AI测试失败: {}", e.getMessage(), e);
            return error("简化AI测试失败: " + e.getMessage());
        }
    }
}
