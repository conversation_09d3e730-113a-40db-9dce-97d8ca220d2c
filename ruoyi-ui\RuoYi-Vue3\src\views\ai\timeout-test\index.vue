<template>
  <div class="timeout-test-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>AI超时配置测试</span>
        </div>
      </template>
      
      <el-form :model="testForm" label-width="120px">
        <el-form-item label="测试消息">
          <el-input
            v-model="testForm.message"
            type="textarea"
            :rows="3"
            placeholder="输入一个复杂的问题来测试AI响应时间"
          />
        </el-form-item>
        
        <el-form-item label="会话ID">
          <el-input
            v-model="testForm.sessionId"
            placeholder="留空将自动生成"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button 
            type="primary" 
            @click="testTimeout"
            :loading="loading"
            :disabled="!testForm.message.trim()"
          >
            {{ loading ? '测试中...' : '开始测试' }}
          </el-button>
          <el-button @click="resetTest">重置</el-button>
        </el-form-item>
      </el-form>
      
      <el-divider>测试结果</el-divider>
      
      <div v-if="testResult">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="开始时间">
            {{ testResult.startTime }}
          </el-descriptions-item>
          <el-descriptions-item label="结束时间">
            {{ testResult.endTime }}
          </el-descriptions-item>
          <el-descriptions-item label="响应时间">
            {{ testResult.duration }}ms
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="testResult.success ? 'success' : 'danger'">
              {{ testResult.success ? '成功' : '失败' }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
        
        <el-divider>响应内容</el-divider>
        
        <el-card v-if="testResult.success" class="response-card">
          <pre>{{ testResult.response }}</pre>
        </el-card>
        
        <el-alert
          v-else
          :title="testResult.error"
          type="error"
          :description="testResult.errorDetail"
          show-icon
        />
      </div>
      
      <el-empty v-else description="暂无测试结果" />
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { sendMessage } from '@/api/ai/chat'

const loading = ref(false)
const testResult = ref(null)

const testForm = reactive({
  message: '请详细解释一下人工智能的发展历史，包括重要的里程碑事件、关键技术突破、主要应用领域，以及对未来的展望。请尽可能详细地回答。',
  sessionId: ''
})

const testTimeout = async () => {
  if (!testForm.message.trim()) {
    ElMessage.warning('请输入测试消息')
    return
  }
  
  loading.value = true
  testResult.value = null
  
  const startTime = new Date()
  const sessionId = testForm.sessionId || 'timeout-test-' + Date.now()
  
  try {
    console.log('开始AI超时测试:', {
      message: testForm.message,
      sessionId: sessionId,
      startTime: startTime.toLocaleString()
    })
    
    const response = await sendMessage({
      sessionId: sessionId,
      message: testForm.message,
      model: 'qwen-plus'
    })
    
    const endTime = new Date()
    const duration = endTime.getTime() - startTime.getTime()
    
    testResult.value = {
      success: true,
      startTime: startTime.toLocaleString(),
      endTime: endTime.toLocaleString(),
      duration: duration,
      response: JSON.stringify(response, null, 2)
    }
    
    ElMessage.success(`测试成功！响应时间: ${duration}ms`)
    
  } catch (error) {
    const endTime = new Date()
    const duration = endTime.getTime() - startTime.getTime()
    
    console.error('AI超时测试失败:', error)
    
    testResult.value = {
      success: false,
      startTime: startTime.toLocaleString(),
      endTime: endTime.toLocaleString(),
      duration: duration,
      error: error.message || '请求失败',
      errorDetail: error.response ? JSON.stringify(error.response.data, null, 2) : error.stack
    }
    
    ElMessage.error(`测试失败！耗时: ${duration}ms`)
  } finally {
    loading.value = false
  }
}

const resetTest = () => {
  testResult.value = null
  testForm.message = '请详细解释一下人工智能的发展历史，包括重要的里程碑事件、关键技术突破、主要应用领域，以及对未来的展望。请尽可能详细地回答。'
  testForm.sessionId = ''
}
</script>

<style scoped>
.timeout-test-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

.response-card {
  margin-top: 10px;
}

.response-card pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  max-height: 400px;
  overflow-y: auto;
}
</style>
