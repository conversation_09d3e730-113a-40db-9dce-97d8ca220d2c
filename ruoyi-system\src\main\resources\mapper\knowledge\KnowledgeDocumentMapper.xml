<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.knowledge.mapper.KnowledgeDocumentMapper">
    
    <resultMap type="KnowledgeDocument" id="KnowledgeDocumentResult">
        <result property="id"                    column="id"                    />
        <result property="name"                  column="name"                  />
        <result property="title"                 column="title"                 />
        <result property="content"               column="content"               />
        <result property="summary"               column="summary"               />
        <result property="knowledgeBaseId"       column="knowledge_base_id"     />
        <result property="folderId"              column="folder_id"             />
        <result property="type"                  column="type"                  />
        <result property="format"                column="format"                />
        <result property="size"                  column="size"                  />
        <result property="status"                column="status"                />
        <result property="processStatus"         column="process_status"        />
        <result property="filePath"              column="file_path"             />
        <result property="fileUrl"               column="file_url"              />
        <result property="tags"                  column="tags"                  />
        <result property="version"               column="version"               />
        <result property="isPublic"              column="is_public"             />
        <result property="viewCount"             column="view_count"            />
        <result property="downloadCount"         column="download_count"        />
        <result property="lastAccessTime"        column="last_access_time"      />
        <result property="creatorId"             column="creator_id"            />
        <result property="creatorName"           column="creator_name"          />
        <result property="createBy"              column="create_by"             />
        <result property="createTime"            column="create_time"           />
        <result property="updateBy"              column="update_by"             />
        <result property="updateTime"            column="update_time"           />
        <result property="remark"                column="remark"                />
    </resultMap>

    <sql id="selectKnowledgeDocumentVo">
        select id, name, title, content, summary, knowledge_base_id, folder_id, type, format, size, status, process_status, file_path, file_url, tags, version, is_public, view_count, download_count, last_access_time, creator_id, creator_name, create_by, create_time, update_by, update_time, remark from knowledge_document
    </sql>

    <select id="selectKnowledgeDocumentList" parameterType="KnowledgeDocument" resultMap="KnowledgeDocumentResult">
        <include refid="selectKnowledgeDocumentVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="knowledgeBaseId != null "> and knowledge_base_id = #{knowledgeBaseId}</if>
            <if test="folderId != null "> and folder_id = #{folderId}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="format != null  and format != ''"> and format = #{format}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="processStatus != null  and processStatus != ''"> and process_status = #{processStatus}</if>
            <if test="tags != null  and tags != ''"> and tags like concat('%', #{tags}, '%')</if>
            <if test="isPublic != null  and isPublic != ''"> and is_public = #{isPublic}</if>
            <if test="creatorId != null "> and creator_id = #{creatorId}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectKnowledgeDocumentById" parameterType="Long" resultMap="KnowledgeDocumentResult">
        <include refid="selectKnowledgeDocumentVo"/>
        where id = #{id}
    </select>

    <select id="selectKnowledgeDocumentByFolderId" parameterType="Long" resultMap="KnowledgeDocumentResult">
        <include refid="selectKnowledgeDocumentVo"/>
        where folder_id = #{folderId}
        order by create_time desc
    </select>

    <select id="selectKnowledgeDocumentByKnowledgeBaseId" parameterType="Long" resultMap="KnowledgeDocumentResult">
        <include refid="selectKnowledgeDocumentVo"/>
        where knowledge_base_id = #{knowledgeBaseId}
        order by create_time desc
    </select>

    <select id="searchKnowledgeDocumentByKeyword" parameterType="String" resultMap="KnowledgeDocumentResult">
        <include refid="selectKnowledgeDocumentVo"/>
        where (name like concat('%', #{keyword}, '%') 
               or title like concat('%', #{keyword}, '%') 
               or summary like concat('%', #{keyword}, '%')
               or content like concat('%', #{keyword}, '%')
               or tags like concat('%', #{keyword}, '%'))
        order by create_time desc
    </select>

    <select id="countDocumentsByFolderId" parameterType="Long" resultType="Long">
        select count(*) from knowledge_document where folder_id = #{folderId}
    </select>

    <select id="countDocumentsByKnowledgeBaseId" parameterType="Long" resultType="Long">
        select count(*) from knowledge_document where knowledge_base_id = #{knowledgeBaseId}
    </select>
        
    <insert id="insertKnowledgeDocument" parameterType="KnowledgeDocument" useGeneratedKeys="true" keyProperty="id">
        insert into knowledge_document
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="content != null">content,</if>
            <if test="summary != null">summary,</if>
            <if test="knowledgeBaseId != null">knowledge_base_id,</if>
            <if test="folderId != null">folder_id,</if>
            <if test="type != null and type != ''">type,</if>
            <if test="format != null and format != ''">format,</if>
            <if test="size != null">size,</if>
            <if test="status != null">status,</if>
            <if test="processStatus != null">process_status,</if>
            <if test="filePath != null">file_path,</if>
            <if test="fileUrl != null">file_url,</if>
            <if test="tags != null">tags,</if>
            <if test="version != null">version,</if>
            <if test="isPublic != null">is_public,</if>
            <if test="viewCount != null">view_count,</if>
            <if test="downloadCount != null">download_count,</if>
            <if test="lastAccessTime != null">last_access_time,</if>
            <if test="creatorId != null">creator_id,</if>
            <if test="creatorName != null">creator_name,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="content != null">#{content},</if>
            <if test="summary != null">#{summary},</if>
            <if test="knowledgeBaseId != null">#{knowledgeBaseId},</if>
            <if test="folderId != null">#{folderId},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="format != null and format != ''">#{format},</if>
            <if test="size != null">#{size},</if>
            <if test="status != null">#{status},</if>
            <if test="processStatus != null">#{processStatus},</if>
            <if test="filePath != null">#{filePath},</if>
            <if test="fileUrl != null">#{fileUrl},</if>
            <if test="tags != null">#{tags},</if>
            <if test="version != null">#{version},</if>
            <if test="isPublic != null">#{isPublic},</if>
            <if test="viewCount != null">#{viewCount},</if>
            <if test="downloadCount != null">#{downloadCount},</if>
            <if test="lastAccessTime != null">#{lastAccessTime},</if>
            <if test="creatorId != null">#{creatorId},</if>
            <if test="creatorName != null">#{creatorName},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateKnowledgeDocument" parameterType="KnowledgeDocument">
        update knowledge_document
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="content != null">content = #{content},</if>
            <if test="summary != null">summary = #{summary},</if>
            <if test="knowledgeBaseId != null">knowledge_base_id = #{knowledgeBaseId},</if>
            <if test="folderId != null">folder_id = #{folderId},</if>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="format != null and format != ''">format = #{format},</if>
            <if test="size != null">size = #{size},</if>
            <if test="status != null">status = #{status},</if>
            <if test="processStatus != null">process_status = #{processStatus},</if>
            <if test="filePath != null">file_path = #{filePath},</if>
            <if test="fileUrl != null">file_url = #{fileUrl},</if>
            <if test="tags != null">tags = #{tags},</if>
            <if test="version != null">version = #{version},</if>
            <if test="isPublic != null">is_public = #{isPublic},</if>
            <if test="viewCount != null">view_count = #{viewCount},</if>
            <if test="downloadCount != null">download_count = #{downloadCount},</if>
            <if test="lastAccessTime != null">last_access_time = #{lastAccessTime},</if>
            <if test="creatorId != null">creator_id = #{creatorId},</if>
            <if test="creatorName != null">creator_name = #{creatorName},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteKnowledgeDocumentById" parameterType="Long">
        delete from knowledge_document where id = #{id}
    </delete>

    <delete id="deleteKnowledgeDocumentByIds" parameterType="String">
        delete from knowledge_document where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateViewCount" parameterType="Long">
        update knowledge_document set view_count = view_count + 1, last_access_time = now() where id = #{id}
    </update>

    <update id="updateDownloadCount" parameterType="Long">
        update knowledge_document set download_count = download_count + 1 where id = #{id}
    </update>
</mapper>
