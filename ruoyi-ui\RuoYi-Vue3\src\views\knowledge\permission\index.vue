<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="知识库" prop="knowledgeBaseId">
        <el-select v-model="queryParams.knowledgeBaseId" placeholder="请选择知识库" clearable>
          <el-option
            v-for="kb in knowledgeBaseList"
            :key="kb.id"
            :label="kb.name"
            :value="kb.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="用户名" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入用户名"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="权限类型" prop="permissionType">
        <el-select v-model="queryParams.permissionType" placeholder="请选择权限类型" clearable>
          <el-option label="只读" value="read" />
          <el-option label="读写" value="write" />
          <el-option label="管理员" value="admin" />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['knowledge:permission:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['knowledge:permission:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['knowledge:permission:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="UserFilled"
          @click="handleBatchGrant"
          v-hasPermi="['knowledge:permission:add']"
        >批量授权</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['knowledge:permission:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="permissionList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="权限ID" align="center" prop="id" />
      <el-table-column label="知识库名称" align="center" prop="knowledgeBaseName" />
      <el-table-column label="用户名称" align="center" prop="userName" />
      <el-table-column label="权限类型" align="center" prop="permissionType">
        <template #default="scope">
          <dict-tag :options="permissionTypeOptions" :value="scope.row.permissionType"/>
        </template>
      </el-table-column>
      <el-table-column label="授权人" align="center" prop="grantedByName" />
      <el-table-column label="授权时间" align="center" prop="grantTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.grantTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <dict-tag :options="sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['knowledge:permission:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['knowledge:permission:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改知识库权限对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="permissionRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="知识库" prop="knowledgeBaseId">
          <el-select v-model="form.knowledgeBaseId" placeholder="请选择知识库">
            <el-option
              v-for="kb in knowledgeBaseList"
              :key="kb.id"
              :label="kb.name"
              :value="kb.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="用户" prop="userId">
          <el-select v-model="form.userId" placeholder="请选择用户" filterable>
            <el-option
              v-for="user in userList"
              :key="user.userId"
              :label="user.userName"
              :value="user.userId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="权限类型" prop="permissionType">
          <el-select v-model="form.permissionType" placeholder="请选择权限类型">
            <el-option label="只读" value="read" />
            <el-option label="读写" value="write" />
            <el-option label="管理员" value="admin" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="0">正常</el-radio>
            <el-radio label="1">停用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量授权对话框 -->
    <el-dialog title="批量授权" v-model="batchGrantOpen" width="600px" append-to-body>
      <el-form ref="batchGrantRef" :model="batchGrantForm" :rules="batchGrantRules" label-width="80px">
        <el-form-item label="知识库" prop="knowledgeBaseId">
          <el-select v-model="batchGrantForm.knowledgeBaseId" placeholder="请选择知识库">
            <el-option
              v-for="kb in knowledgeBaseList"
              :key="kb.id"
              :label="kb.name"
              :value="kb.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="用户" prop="userIds">
          <el-select v-model="batchGrantForm.userIds" placeholder="请选择用户" multiple filterable>
            <el-option
              v-for="user in userList"
              :key="user.userId"
              :label="user.userName"
              :value="user.userId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="权限类型" prop="permissionType">
          <el-select v-model="batchGrantForm.permissionType" placeholder="请选择权限类型">
            <el-option label="只读" value="read" />
            <el-option label="读写" value="write" />
            <el-option label="管理员" value="admin" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitBatchGrant">确 定</el-button>
          <el-button @click="cancelBatchGrant">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="KnowledgePermission">
import { listPermission, getPermission, delPermission, addPermission, updatePermission, batchGrantPermissions } from "@/api/knowledge/permission";
import { listKnowledgeBase } from "@/api/knowledge/permission";
import { listUser } from "@/api/system/user";

const { proxy } = getCurrentInstance();
const { sys_normal_disable } = proxy.useDict('sys_normal_disable');

const permissionList = ref([]);
const knowledgeBaseList = ref([]);
const userList = ref([]);
const open = ref(false);
const batchGrantOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  batchGrantForm: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    knowledgeBaseId: null,
    userName: null,
    permissionType: null,
    status: null
  },
  rules: {
    knowledgeBaseId: [
      { required: true, message: "知识库不能为空", trigger: "change" }
    ],
    userId: [
      { required: true, message: "用户不能为空", trigger: "change" }
    ],
    permissionType: [
      { required: true, message: "权限类型不能为空", trigger: "change" }
    ]
  },
  batchGrantRules: {
    knowledgeBaseId: [
      { required: true, message: "知识库不能为空", trigger: "change" }
    ],
    userIds: [
      { required: true, message: "用户不能为空", trigger: "change" }
    ],
    permissionType: [
      { required: true, message: "权限类型不能为空", trigger: "change" }
    ]
  }
});

const { queryParams, form, batchGrantForm, rules, batchGrantRules } = toRefs(data);

// 权限类型选项
const permissionTypeOptions = ref([
  { label: "只读", value: "read" },
  { label: "读写", value: "write" },
  { label: "管理员", value: "admin" }
]);

/** 查询知识库权限列表 */
function getList() {
  loading.value = true;
  listPermission(queryParams.value).then(response => {
    permissionList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 取消批量授权
function cancelBatchGrant() {
  batchGrantOpen.value = false;
  resetBatchGrant();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    knowledgeBaseId: null,
    userId: null,
    userName: null,
    permissionType: null,
    status: "0",
    remark: null
  };
  proxy.resetForm("permissionRef");
}

// 批量授权表单重置
function resetBatchGrant() {
  batchGrantForm.value = {
    knowledgeBaseId: null,
    userIds: [],
    permissionType: null
  };
  proxy.resetForm("batchGrantRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加知识库权限";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const id = row.id || ids.value
  getPermission(id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改知识库权限";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["permissionRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updatePermission(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addPermission(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除知识库权限编号为"' + _ids + '"的数据项？').then(function() {
    return delPermission(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 批量授权按钮操作 */
function handleBatchGrant() {
  resetBatchGrant();
  batchGrantOpen.value = true;
}

/** 提交批量授权 */
function submitBatchGrant() {
  proxy.$refs["batchGrantRef"].validate(valid => {
    if (valid) {
      batchGrantPermissions(batchGrantForm.value).then(response => {
        proxy.$modal.msgSuccess("批量授权成功");
        batchGrantOpen.value = false;
        getList();
      });
    }
  });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('knowledge/permission/export', {
    ...queryParams.value
  }, `permission_${new Date().getTime()}.xlsx`)
}

/** 初始化数据 */
function initData() {
  // 获取知识库列表
  listKnowledgeBase().then(response => {
    knowledgeBaseList.value = response.rows || response.data || [];
  });
  
  // 获取用户列表
  listUser().then(response => {
    userList.value = response.rows || response.data || [];
  });
}

onMounted(() => {
  getList();
  initData();
});
</script>
