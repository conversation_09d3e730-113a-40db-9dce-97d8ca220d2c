package com.ruoyi.knowledge.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * 策略执行日志对象 knowledge_strategy_execution_log
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
public class KnowledgeStrategyExecutionLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 日志ID */
    private Long id;

    /** 知识库ID */
    @Excel(name = "知识库ID")
    private Long knowledgeBaseId;

    /** 策略配置ID */
    @Excel(name = "策略配置ID")
    private Long strategyConfigId;

    /** 策略类型 */
    @Excel(name = "策略类型", readConverterExp = "INITIALIZATION=初始化策略,SEGMENTATION=段落切分策略,TAGGING=标签策略,SUMMARIZATION=归纳总结策略,ASSOCIATION=关联策略")
    private String strategyType;

    /** 执行状态 */
    @Excel(name = "执行状态", readConverterExp = "PENDING=待执行,RUNNING=执行中,SUCCESS=成功,FAILED=失败")
    private String executionStatus;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 执行时长(毫秒) */
    @Excel(name = "执行时长(毫秒)")
    private Long durationMs;

    /** 输入数据 */
    private String inputData;

    /** 输出数据 */
    private String outputData;

    /** 错误信息 */
    private String errorMessage;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setKnowledgeBaseId(Long knowledgeBaseId) 
    {
        this.knowledgeBaseId = knowledgeBaseId;
    }

    public Long getKnowledgeBaseId() 
    {
        return knowledgeBaseId;
    }

    public void setStrategyConfigId(Long strategyConfigId) 
    {
        this.strategyConfigId = strategyConfigId;
    }

    public Long getStrategyConfigId() 
    {
        return strategyConfigId;
    }

    public void setStrategyType(String strategyType) 
    {
        this.strategyType = strategyType;
    }

    public String getStrategyType() 
    {
        return strategyType;
    }

    public void setExecutionStatus(String executionStatus) 
    {
        this.executionStatus = executionStatus;
    }

    public String getExecutionStatus() 
    {
        return executionStatus;
    }

    public void setStartTime(Date startTime) 
    {
        this.startTime = startTime;
    }

    public Date getStartTime() 
    {
        return startTime;
    }

    public void setEndTime(Date endTime) 
    {
        this.endTime = endTime;
    }

    public Date getEndTime() 
    {
        return endTime;
    }

    public void setDurationMs(Long durationMs) 
    {
        this.durationMs = durationMs;
    }

    public Long getDurationMs() 
    {
        return durationMs;
    }

    public void setInputData(String inputData) 
    {
        this.inputData = inputData;
    }

    public String getInputData() 
    {
        return inputData;
    }

    public void setOutputData(String outputData) 
    {
        this.outputData = outputData;
    }

    public String getOutputData() 
    {
        return outputData;
    }

    public void setErrorMessage(String errorMessage) 
    {
        this.errorMessage = errorMessage;
    }

    public String getErrorMessage() 
    {
        return errorMessage;
    }

    @Override
    public String toString() {
        return "KnowledgeStrategyExecutionLog{" +
                "id=" + id +
                ", knowledgeBaseId=" + knowledgeBaseId +
                ", strategyConfigId=" + strategyConfigId +
                ", strategyType='" + strategyType + '\'' +
                ", executionStatus='" + executionStatus + '\'' +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", durationMs=" + durationMs +
                ", inputData='" + inputData + '\'' +
                ", outputData='" + outputData + '\'' +
                ", errorMessage='" + errorMessage + '\'' +
                '}';
    }
}
