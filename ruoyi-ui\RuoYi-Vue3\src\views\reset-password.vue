<template>
  <div class="login">
    <el-form ref="resetPasswordRef" :model="resetPasswordForm" :rules="resetPasswordRules" class="login-form">
      <h3 class="title">重置密码</h3>

      <!-- 第一步：验证身份 -->
      <div v-if="currentStep === 1">
        <el-form-item prop="username">
          <el-input
            v-model="resetPasswordForm.username"
            type="text"
            size="large"
            auto-complete="off"
            placeholder="请输入账户名"
          >
            <template #prefix><svg-icon icon-class="user" class="el-input__icon input-icon" /></template>
          </el-input>
        </el-form-item>

        <el-form-item prop="phonenumber">
          <el-input
            v-model="resetPasswordForm.phonenumber"
            type="text"
            size="large"
            auto-complete="off"
            placeholder="请输入注册时的手机号"
            maxlength="11"
          >
            <template #prefix><svg-icon icon-class="phone" class="el-input__icon input-icon" /></template>
          </el-input>
        </el-form-item>
        
        <el-form-item prop="smsCode">
          <el-input
            v-model="resetPasswordForm.smsCode"
            size="large"
            auto-complete="off"
            placeholder="验证码"
            style="width: 63%"
            maxlength="6"
          >
            <template #prefix><svg-icon icon-class="validCode" class="el-input__icon input-icon" /></template>
          </el-input>
          <div class="login-code">
            <el-button
              type="primary"
              size="large"
              :disabled="smsCountdown > 0"
              @click="sendSmsCode"
              :loading="sendingSms"
              class="sms-button"
            >
              {{ smsCountdown > 0 ? `${smsCountdown}s` : '发送验证码' }}
            </el-button>
          </div>
        </el-form-item>

        <el-form-item style="width:100%;">
          <el-button
            :loading="loading"
            size="large"
            type="primary"
            style="width:100%;"
            @click="verifyPhone"
          >
            <span v-if="!loading">验证身份</span>
            <span v-else>验证中...</span>
          </el-button>
        </el-form-item>
      </div>

      <!-- 第二步：设置新密码 -->
      <div v-if="currentStep === 2">
        <el-form-item prop="newPassword">
          <el-input
            v-model="resetPasswordForm.newPassword"
            type="password"
            size="large"
            auto-complete="off"
            placeholder="请输入新密码"
            show-password
          >
            <template #prefix><svg-icon icon-class="password" class="el-input__icon input-icon" /></template>
          </el-input>
        </el-form-item>

        <el-form-item prop="confirmNewPassword">
          <el-input
            v-model="resetPasswordForm.confirmNewPassword"
            type="password"
            size="large"
            auto-complete="off"
            placeholder="请确认新密码"
            show-password
          >
            <template #prefix><svg-icon icon-class="password" class="el-input__icon input-icon" /></template>
          </el-input>
        </el-form-item>

        <el-form-item style="width:100%;">
          <el-button
            :loading="loading"
            size="large"
            type="primary"
            style="width:100%;"
            @click="resetPassword"
          >
            <span v-if="!loading">重置密码</span>
            <span v-else>重置中...</span>
          </el-button>
        </el-form-item>
      </div>

      <!-- 第三步：完成 -->
      <div v-if="currentStep === 3" style="text-align: center; padding: 20px 0;">
        <div style="margin: 30px 0;">
          <div style="font-size: 60px; color: #67C23A; margin-bottom: 20px;">✓</div>
        </div>
        <h4 style="color: #67C23A; margin-bottom: 20px;">密码重置成功！</h4>
        <p style="color: #666; margin-bottom: 30px; font-size: 14px;">您的密码已成功重置，请使用新密码登录</p>
        <el-form-item style="width:100%;">
          <el-button
            size="large"
            type="primary"
            style="width:100%;"
            @click="goToLogin"
          >
            返回登录
          </el-button>
        </el-form-item>
      </div>

      <!-- 返回登录链接 -->
      <el-form-item v-if="currentStep < 3">
        <div style="width: 100%; text-align: center;">
          <router-link class="link-type" :to="'/login'">返回登录</router-link>
        </div>
      </el-form-item>
    </el-form>
    
    <!--  底部  -->
    <div class="el-login-footer">
      <span>Copyright © 2018-2023 ruoyi.vip All Rights Reserved.</span>
    </div>
  </div>
</template>

<script setup>
import { ElMessage } from "element-plus";
import { ref, getCurrentInstance } from 'vue'
import { useRouter } from 'vue-router'
import { sendSmsCode as sendSmsCodeApi, verifyPhone as verifyPhoneApi, resetPassword as resetPasswordApi } from '@/api/login'

const router = useRouter();
const { proxy } = getCurrentInstance();

// 当前步骤
const currentStep = ref(1);

// 表单数据
const resetPasswordForm = ref({
  username: "",
  phonenumber: "",
  smsCode: "",
  newPassword: "",
  confirmNewPassword: "",
  resetToken: ""
});

// 加载状态
const loading = ref(false);
const sendingSms = ref(false);
const smsCountdown = ref(0);

// 密码确认验证
const validateConfirmPassword = (rule, value, callback) => {
  if (value !== resetPasswordForm.value.newPassword) {
    callback(new Error("两次输入的密码不一致"));
  } else {
    callback();
  }
};

// 表单验证规则
const resetPasswordRules = {
  username: [
    { required: true, trigger: "blur", message: "请输入账户名" },
    { min: 2, max: 20, message: "账户名长度必须介于 2 和 20 之间", trigger: "blur" }
  ],
  phonenumber: [
    { required: true, trigger: "blur", message: "请输入手机号码" },
    { pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: "请输入正确的手机号码", trigger: "blur" }
  ],
  smsCode: [
    { required: true, trigger: "blur", message: "请输入短信验证码" },
    { min: 6, max: 6, message: "验证码长度为6位", trigger: "blur" }
  ],
  newPassword: [
    { required: true, trigger: "blur", message: "请输入新密码" },
    { min: 5, max: 20, message: "密码长度必须介于 5 和 20 之间", trigger: "blur" }
  ],
  confirmNewPassword: [
    { required: true, trigger: "blur", message: "请确认新密码" },
    { validator: validateConfirmPassword, trigger: "blur" }
  ]
};

// 发送短信验证码
function sendSmsCode() {
  if (!resetPasswordForm.value.username) {
    ElMessage.error("请先输入账户名");
    return;
  }

  if (!resetPasswordForm.value.phonenumber) {
    ElMessage.error("请先输入手机号码");
    return;
  }

  if (!/^1[3|4|5|6|7|8|9][0-9]\d{8}$/.test(resetPasswordForm.value.phonenumber)) {
    ElMessage.error("请输入正确的手机号码");
    return;
  }

  sendingSms.value = true;

  // 调用短信API
  const smsData = {
    username: resetPasswordForm.value.username,
    phonenumber: resetPasswordForm.value.phonenumber,
    type: 'reset_password' // 标识这是重置密码的短信
  };

  sendSmsCodeApi(smsData).then(response => {
    sendingSms.value = false;
    ElMessage.success(`验证码已发送至 ${resetPasswordForm.value.phonenumber}，请注意查收`);

    // 开始倒计时
    smsCountdown.value = 60;
    const timer = setInterval(() => {
      smsCountdown.value--;
      if (smsCountdown.value <= 0) {
        clearInterval(timer);
      }
    }, 1000);
  }).catch(error => {
    sendingSms.value = false;
    ElMessage.error(error.msg || "发送验证码失败，请稍后重试");
  });
}

// 验证身份信息
function verifyPhone() {
  proxy.$refs.resetPasswordRef.validate(valid => {
    if (valid) {
      loading.value = true;

      // 调用验证API
      const verifyData = {
        username: resetPasswordForm.value.username,
        phonenumber: resetPasswordForm.value.phonenumber,
        smsCode: resetPasswordForm.value.smsCode
      };

      verifyPhoneApi(verifyData).then(response => {
        loading.value = false;
        // 保存验证token，用于后续重置密码
        resetPasswordForm.value.resetToken = response.data.resetToken || response.resetToken;
        ElMessage.success("身份验证成功");
        currentStep.value = 2;
      }).catch(error => {
        loading.value = false;
        ElMessage.error(error.msg || "身份验证失败，请检查输入信息");
      });
    }
  });
}

// 重置密码
function resetPassword() {
  proxy.$refs.resetPasswordRef.validate(valid => {
    if (valid) {
      loading.value = true;

      // 调用重置密码API
      const resetData = {
        resetToken: resetPasswordForm.value.resetToken,
        newPassword: resetPasswordForm.value.newPassword,
        confirmPassword: resetPasswordForm.value.confirmNewPassword
      };

      resetPasswordApi(resetData).then(response => {
        loading.value = false;
        ElMessage.success("密码重置成功");
        currentStep.value = 3;
      }).catch(error => {
        loading.value = false;
        ElMessage.error(error.msg || "密码重置失败，请稍后重试");
      });
    }
  });
}

// 跳转到登录页面
function goToLogin() {
  router.push('/login');
}
</script>

<style lang='scss' scoped>
.login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-image: url("../assets/images/login-background.jpg");
  background-size: cover;
}
.title {
  margin: 0px auto 30px auto;
  text-align: center;
  color: #707070;
}

.login-form {
  border-radius: 6px;
  background: #ffffff;
  width: 400px;
  padding: 25px 25px 5px 25px;
  .el-input {
    height: 40px;
    input {
      height: 40px;
    }
  }
  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 0px;
  }
}
.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}
.login-code {
  width: 33%;
  height: 40px;
  float: right;
  .sms-button {
    width: 100%;
    height: 40px;
    font-size: 12px;
  }
  img {
    cursor: pointer;
    vertical-align: middle;
  }
}
.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}
.login-code-img {
  height: 40px;
  padding-left: 12px;
}
.link-type {
  color: #409EFF;
  text-decoration: none;

  &:hover {
    color: #66b1ff;
  }
}
</style>
