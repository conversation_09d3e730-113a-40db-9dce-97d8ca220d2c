<template>
  <div class="rag-test-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>RAG功能测试</span>
        </div>
      </template>
      
      <el-form :model="testForm" label-width="120px">
        <el-form-item label="选择知识库">
          <el-select v-model="testForm.knowledgeBaseId" placeholder="请选择知识库" style="width: 300px;">
            <el-option 
              v-for="kb in knowledgeBaseList" 
              :key="kb.id" 
              :label="kb.name" 
              :value="kb.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="测试问题">
          <el-input 
            v-model="testForm.question" 
            type="textarea" 
            :rows="3" 
            placeholder="请输入您的问题..."
            style="width: 500px;"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="testRag" :loading="loading">
            测试RAG回答
          </el-button>
          <el-button @click="testNormalChat" :loading="loading">
            测试普通回答
          </el-button>
          <el-button @click="clearResults">清空结果</el-button>
        </el-form-item>
      </el-form>
      
      <el-divider />
      
      <div v-if="results.length > 0">
        <h3>测试结果对比</h3>
        <div v-for="(result, index) in results" :key="index" class="result-item">
          <el-card :class="result.type === 'rag' ? 'rag-result' : 'normal-result'">
            <template #header>
              <div class="result-header">
                <span>{{ result.type === 'rag' ? 'RAG增强回答' : '普通回答' }}</span>
                <el-tag :type="result.type === 'rag' ? 'success' : 'info'">
                  {{ result.type === 'rag' ? '使用知识库' : '不使用知识库' }}
                </el-tag>
              </div>
            </template>
            <div class="result-content">
              <p><strong>问题：</strong>{{ result.question }}</p>
              <p><strong>回答：</strong></p>
              <div class="answer" v-html="formatAnswer(result.answer)"></div>
              <p class="meta-info">
                <span>响应时间: {{ result.responseTime }}ms</span>
                <span v-if="result.knowledgeBase">| 知识库: {{ result.knowledgeBase }}</span>
              </p>
            </div>
          </el-card>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup name="RagTest">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { listKnowledgeBase } from '@/api/knowledge/build'
import { sendMessage } from '@/api/ai/chat'

// 数据
const loading = ref(false)
const knowledgeBaseList = ref([])
const testForm = ref({
  knowledgeBaseId: null,
  question: ''
})
const results = ref([])

// 方法
const loadKnowledgeBaseList = async () => {
  try {
    const response = await listKnowledgeBase({})
    knowledgeBaseList.value = response.rows || []
  } catch (error) {
    ElMessage.error('加载知识库列表失败')
  }
}

const testRag = async () => {
  if (!testForm.value.knowledgeBaseId) {
    ElMessage.warning('请选择知识库')
    return
  }
  if (!testForm.value.question.trim()) {
    ElMessage.warning('请输入测试问题')
    return
  }
  
  loading.value = true
  try {
    const startTime = Date.now()
    const response = await sendMessage({
      sessionId: 'test-session-' + Date.now(),
      message: testForm.value.question,
      model: 'qwen-plus',
      knowledgeBaseId: testForm.value.knowledgeBaseId
    })
    
    const endTime = Date.now()
    const kb = knowledgeBaseList.value.find(item => item.id === testForm.value.knowledgeBaseId)
    
    results.value.unshift({
      type: 'rag',
      question: testForm.value.question,
      answer: response.data.content,
      responseTime: endTime - startTime,
      knowledgeBase: kb ? kb.name : '未知'
    })
    
    ElMessage.success('RAG测试完成')
  } catch (error) {
    ElMessage.error('RAG测试失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const testNormalChat = async () => {
  if (!testForm.value.question.trim()) {
    ElMessage.warning('请输入测试问题')
    return
  }
  
  loading.value = true
  try {
    const startTime = Date.now()
    const response = await sendMessage({
      sessionId: 'test-session-' + Date.now(),
      message: testForm.value.question,
      model: 'qwen-plus'
      // 不传 knowledgeBaseId
    })
    
    const endTime = Date.now()
    
    results.value.unshift({
      type: 'normal',
      question: testForm.value.question,
      answer: response.data.content,
      responseTime: endTime - startTime
    })
    
    ElMessage.success('普通聊天测试完成')
  } catch (error) {
    ElMessage.error('普通聊天测试失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const clearResults = () => {
  results.value = []
  ElMessage.info('已清空测试结果')
}

const formatAnswer = (answer) => {
  return answer.replace(/\n/g, '<br>')
}

// 生命周期
onMounted(() => {
  loadKnowledgeBaseList()
})
</script>

<style lang="scss" scoped>
.rag-test-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.result-item {
  margin-bottom: 20px;
}

.rag-result {
  border-left: 4px solid #67c23a;
}

.normal-result {
  border-left: 4px solid #909399;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.result-content {
  .answer {
    background-color: #f5f7fa;
    padding: 12px;
    border-radius: 4px;
    margin: 8px 0;
    line-height: 1.6;
  }
  
  .meta-info {
    font-size: 12px;
    color: #909399;
    margin-top: 8px;
    
    span {
      margin-right: 16px;
    }
  }
}
</style>
