package com.ruoyi.knowledge.domain;

import java.util.Date;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 知识库文件夹对象 knowledge_folder
 * 
 * <AUTHOR>
 * @date 2024-07-10
 */
public class KnowledgeFolder extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 文件夹ID */
    private Long id;

    /** 文件夹名称 */
    @Excel(name = "文件夹名称")
    private String name;

    /** 文件夹描述 */
    @Excel(name = "文件夹描述")
    private String description;

    /** 父文件夹ID */
    @Excel(name = "父文件夹ID")
    private Long parentId;

    /** 所属知识库ID */
    @Excel(name = "所属知识库ID")
    private Long knowledgeBaseId;

    /** 文件夹路径 */
    @Excel(name = "文件夹路径")
    private String path;

    /** 文件夹层级 */
    @Excel(name = "文件夹层级")
    private Integer level;

    /** 排序号 */
    @Excel(name = "排序号")
    private Integer orderNum;

    /** 文件夹状态（0正常 1停用） */
    @Excel(name = "文件夹状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 是否公开（0私有 1公开） */
    @Excel(name = "是否公开", readConverterExp = "0=私有,1=公开")
    private String isPublic;

    /** 文档数量 */
    @Excel(name = "文档数量")
    private Long documentCount;

    /** 子文件夹数量 */
    @Excel(name = "子文件夹数量")
    private Long subFolderCount;

    /** 创建者ID */
    @Excel(name = "创建者ID")
    private Long creatorId;

    /** 创建者名称 */
    @Excel(name = "创建者名称")
    private String creatorName;

    /** 子文件夹列表 */
    private List<KnowledgeFolder> children;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }
    public void setDescription(String description) 
    {
        this.description = description;
    }

    public String getDescription() 
    {
        return description;
    }
    public void setParentId(Long parentId) 
    {
        this.parentId = parentId;
    }

    public Long getParentId() 
    {
        return parentId;
    }
    public void setKnowledgeBaseId(Long knowledgeBaseId) 
    {
        this.knowledgeBaseId = knowledgeBaseId;
    }

    public Long getKnowledgeBaseId() 
    {
        return knowledgeBaseId;
    }
    public void setPath(String path) 
    {
        this.path = path;
    }

    public String getPath() 
    {
        return path;
    }
    public void setLevel(Integer level) 
    {
        this.level = level;
    }

    public Integer getLevel() 
    {
        return level;
    }
    public void setOrderNum(Integer orderNum) 
    {
        this.orderNum = orderNum;
    }

    public Integer getOrderNum() 
    {
        return orderNum;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setIsPublic(String isPublic) 
    {
        this.isPublic = isPublic;
    }

    public String getIsPublic() 
    {
        return isPublic;
    }
    public void setDocumentCount(Long documentCount) 
    {
        this.documentCount = documentCount;
    }

    public Long getDocumentCount() 
    {
        return documentCount;
    }
    public void setSubFolderCount(Long subFolderCount) 
    {
        this.subFolderCount = subFolderCount;
    }

    public Long getSubFolderCount() 
    {
        return subFolderCount;
    }
    public void setCreatorId(Long creatorId) 
    {
        this.creatorId = creatorId;
    }

    public Long getCreatorId() 
    {
        return creatorId;
    }
    public void setCreatorName(String creatorName) 
    {
        this.creatorName = creatorName;
    }

    public String getCreatorName() 
    {
        return creatorName;
    }
    public void setChildren(List<KnowledgeFolder> children) 
    {
        this.children = children;
    }

    public List<KnowledgeFolder> getChildren() 
    {
        return children;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("name", getName())
            .append("description", getDescription())
            .append("parentId", getParentId())
            .append("knowledgeBaseId", getKnowledgeBaseId())
            .append("path", getPath())
            .append("level", getLevel())
            .append("orderNum", getOrderNum())
            .append("status", getStatus())
            .append("isPublic", getIsPublic())
            .append("documentCount", getDocumentCount())
            .append("subFolderCount", getSubFolderCount())
            .append("creatorId", getCreatorId())
            .append("creatorName", getCreatorName())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
