package com.ruoyi.knowledge.strategy;

import com.ruoyi.knowledge.enums.StrategyType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 策略工厂
 * 负责管理和创建各种知识库策略实例
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
@Component
public class StrategyFactory {
    
    @Autowired
    private ApplicationContext applicationContext;
    
    /** 策略实例缓存 */
    private final Map<String, KnowledgeStrategy<?, ?>> strategyCache = new ConcurrentHashMap<>();
    
    /** 按类型分组的策略映射 */
    private final Map<StrategyType, List<KnowledgeStrategy<?, ?>>> strategyByType = new ConcurrentHashMap<>();
    
    @PostConstruct
    public void init() {
        // 从Spring容器中获取所有策略实现
        Map<String, KnowledgeStrategy> strategies = applicationContext.getBeansOfType(KnowledgeStrategy.class);
        
        for (Map.Entry<String, KnowledgeStrategy> entry : strategies.entrySet()) {
            KnowledgeStrategy<?, ?> strategy = entry.getValue();
            String strategyKey = generateStrategyKey(strategy.getStrategyType(), strategy.getStrategyName());
            
            // 缓存策略实例
            strategyCache.put(strategyKey, strategy);
            
            // 按类型分组
            strategyByType.computeIfAbsent(strategy.getStrategyType(), k -> new ArrayList<>()).add(strategy);
        }
        
        // 按优先级排序
        for (List<KnowledgeStrategy<?, ?>> strategies1 : strategyByType.values()) {
            strategies1.sort(Comparator.comparingInt(KnowledgeStrategy::getExecutionPriority));
        }
    }
    
    /**
     * 根据策略类型和名称获取策略实例
     * 
     * @param strategyType 策略类型
     * @param strategyName 策略名称
     * @return 策略实例
     */
    public KnowledgeStrategy<?, ?> getStrategy(StrategyType strategyType, String strategyName) {
        String strategyKey = generateStrategyKey(strategyType, strategyName);
        return strategyCache.get(strategyKey);
    }
    
    /**
     * 根据策略类型获取默认策略
     * 
     * @param strategyType 策略类型
     * @return 默认策略实例
     */
    public KnowledgeStrategy<?, ?> getDefaultStrategy(StrategyType strategyType) {
        List<KnowledgeStrategy<?, ?>> strategies = strategyByType.get(strategyType);
        if (strategies != null && !strategies.isEmpty()) {
            // 返回优先级最高的策略作为默认策略
            return strategies.get(0);
        }
        return null;
    }
    
    /**
     * 根据策略类型获取所有策略
     * 
     * @param strategyType 策略类型
     * @return 策略列表
     */
    public List<KnowledgeStrategy<?, ?>> getStrategiesByType(StrategyType strategyType) {
        return strategyByType.getOrDefault(strategyType, Collections.emptyList());
    }
    
    /**
     * 获取所有策略类型
     * 
     * @return 策略类型集合
     */
    public Set<StrategyType> getAllStrategyTypes() {
        return strategyByType.keySet();
    }
    
    /**
     * 获取所有策略
     * 
     * @return 策略映射
     */
    public Map<String, KnowledgeStrategy<?, ?>> getAllStrategies() {
        return new HashMap<>(strategyCache);
    }
    
    /**
     * 检查策略是否存在
     * 
     * @param strategyType 策略类型
     * @param strategyName 策略名称
     * @return 是否存在
     */
    public boolean hasStrategy(StrategyType strategyType, String strategyName) {
        String strategyKey = generateStrategyKey(strategyType, strategyName);
        return strategyCache.containsKey(strategyKey);
    }
    
    /**
     * 生成策略键
     * 
     * @param strategyType 策略类型
     * @param strategyName 策略名称
     * @return 策略键
     */
    private String generateStrategyKey(StrategyType strategyType, String strategyName) {
        return strategyType.getCode() + ":" + strategyName;
    }
}
