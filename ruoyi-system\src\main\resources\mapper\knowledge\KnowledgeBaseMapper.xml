<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.knowledge.mapper.KnowledgeBaseMapper">
    
    <resultMap type="KnowledgeBase" id="KnowledgeBaseResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="description"    column="description"    />
        <result property="folderId"    column="folder_id"    />
        <result property="type"    column="type"    />
        <result property="status"    column="status"    />
        <result property="documentCount"    column="document_count"    />
        <result property="size"    column="size"    />
        <result property="lastUpdateTime"    column="last_update_time"    />
        <result property="creatorId"    column="creator_id"    />
        <result property="creatorName"    column="creator_name"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectKnowledgeBaseVo">
        select id, name, description, folder_id, type, status, document_count, size, last_update_time, creator_id, creator_name, create_by, create_time, update_by, update_time, remark from knowledge_base
    </sql>

    <select id="selectKnowledgeBaseList" parameterType="KnowledgeBase" resultMap="KnowledgeBaseResult">
        <include refid="selectKnowledgeBaseVo"/>
        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="description != null  and description != ''"> and description like concat('%', #{description}, '%')</if>
            <if test="folderId != null "> and folder_id = #{folderId}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="creatorId != null "> and creator_id = #{creatorId}</if>
            <if test="creatorName != null  and creatorName != ''"> and creator_name like concat('%', #{creatorName}, '%')</if>
            <if test="ids != null and ids.length > 0">
                and id in
                <foreach item="id" collection="ids" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="excludeCreatorId != null "> and creator_id != #{excludeCreatorId}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectKnowledgeBaseById" parameterType="Long" resultMap="KnowledgeBaseResult">
        <include refid="selectKnowledgeBaseVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertKnowledgeBase" parameterType="KnowledgeBase" useGeneratedKeys="true" keyProperty="id">
        insert into knowledge_base
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="description != null">description,</if>
            <if test="folderId != null">folder_id,</if>
            <if test="type != null and type != ''">type,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="documentCount != null">document_count,</if>
            <if test="size != null">size,</if>
            <if test="lastUpdateTime != null">last_update_time,</if>
            <if test="creatorId != null">creator_id,</if>
            <if test="creatorName != null and creatorName != ''">creator_name,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="description != null">#{description},</if>
            <if test="folderId != null">#{folderId},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="documentCount != null">#{documentCount},</if>
            <if test="size != null">#{size},</if>
            <if test="lastUpdateTime != null">#{lastUpdateTime},</if>
            <if test="creatorId != null">#{creatorId},</if>
            <if test="creatorName != null and creatorName != ''">#{creatorName},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateKnowledgeBase" parameterType="KnowledgeBase">
        update knowledge_base
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="description != null">description = #{description},</if>
            <if test="folderId != null">folder_id = #{folderId},</if>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="documentCount != null">document_count = #{documentCount},</if>
            <if test="size != null">size = #{size},</if>
            <if test="lastUpdateTime != null">last_update_time = #{lastUpdateTime},</if>
            <if test="creatorId != null">creator_id = #{creatorId},</if>
            <if test="creatorName != null and creatorName != ''">creator_name = #{creatorName},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteKnowledgeBaseById" parameterType="Long">
        delete from knowledge_base where id = #{id}
    </delete>

    <delete id="deleteKnowledgeBaseByIds" parameterType="String">
        delete from knowledge_base where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
