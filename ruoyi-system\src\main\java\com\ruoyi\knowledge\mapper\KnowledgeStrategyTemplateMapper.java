package com.ruoyi.knowledge.mapper;

import com.ruoyi.knowledge.domain.KnowledgeStrategyTemplate;
import java.util.List;

/**
 * 知识库策略模板Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
public interface KnowledgeStrategyTemplateMapper 
{
    /**
     * 查询知识库策略模板
     * 
     * @param id 知识库策略模板主键
     * @return 知识库策略模板
     */
    public KnowledgeStrategyTemplate selectKnowledgeStrategyTemplateById(Long id);

    /**
     * 查询知识库策略模板列表
     * 
     * @param knowledgeStrategyTemplate 知识库策略模板
     * @return 知识库策略模板集合
     */
    public List<KnowledgeStrategyTemplate> selectKnowledgeStrategyTemplateList(KnowledgeStrategyTemplate knowledgeStrategyTemplate);

    /**
     * 新增知识库策略模板
     * 
     * @param knowledgeStrategyTemplate 知识库策略模板
     * @return 结果
     */
    public int insertKnowledgeStrategyTemplate(KnowledgeStrategyTemplate knowledgeStrategyTemplate);

    /**
     * 修改知识库策略模板
     * 
     * @param knowledgeStrategyTemplate 知识库策略模板
     * @return 结果
     */
    public int updateKnowledgeStrategyTemplate(KnowledgeStrategyTemplate knowledgeStrategyTemplate);

    /**
     * 删除知识库策略模板
     * 
     * @param id 知识库策略模板主键
     * @return 结果
     */
    public int deleteKnowledgeStrategyTemplateById(Long id);

    /**
     * 批量删除知识库策略模板
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteKnowledgeStrategyTemplateByIds(Long[] ids);

    /**
     * 根据策略类型查询策略模板
     * 
     * @param strategyType 策略类型
     * @return 策略模板列表
     */
    public List<KnowledgeStrategyTemplate> selectKnowledgeStrategyTemplateByType(String strategyType);

    /**
     * 根据策略类型查询默认策略模板
     * 
     * @param strategyType 策略类型
     * @return 默认策略模板
     */
    public KnowledgeStrategyTemplate selectDefaultKnowledgeStrategyTemplateByType(String strategyType);

    /**
     * 查询启用的策略模板
     * 
     * @return 启用的策略模板列表
     */
    public List<KnowledgeStrategyTemplate> selectEnabledKnowledgeStrategyTemplates();
}
