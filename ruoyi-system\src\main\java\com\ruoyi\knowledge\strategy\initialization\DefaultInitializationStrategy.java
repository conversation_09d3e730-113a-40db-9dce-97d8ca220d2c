package com.ruoyi.knowledge.strategy.initialization;

import com.fasterxml.jackson.databind.JsonNode;
import com.ruoyi.knowledge.strategy.AbstractKnowledgeStrategy;
import org.springframework.stereotype.Component;

/**
 * 默认初始化策略实现
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
@Component
public class DefaultInitializationStrategy extends AbstractKnowledgeStrategy<InitializationContext, InitializationResult> 
        implements InitializationStrategy {
    
    @Override
    public String getStrategyName() {
        return "默认初始化策略";
    }
    
    @Override
    public String getStrategyDescription() {
        return "知识库的默认初始化策略，包含基础的创建和配置流程";
    }
    
    @Override
    public String getDefaultConfig() {
        return "{"
                + "\"enableVectorization\": true,"
                + "\"enableMetadata\": true,"
                + "\"batchSize\": 100,"
                + "\"autoProcessDocuments\": true,"
                + "\"createIndexes\": true"
                + "}";
    }
    
    @Override
    protected InitializationResult doExecute(InitializationContext input, String config) throws Exception {
        JsonNode configNode = parseConfig(config);
        
        boolean enableVectorization = getConfigBoolean(configNode, "enableVectorization", true);
        boolean enableMetadata = getConfigBoolean(configNode, "enableMetadata", true);
        int batchSize = getConfigInt(configNode, "batchSize", 100);
        boolean autoProcessDocuments = getConfigBoolean(configNode, "autoProcessDocuments", true);
        boolean createIndexes = getConfigBoolean(configNode, "createIndexes", true);
        
        logger.info("开始执行默认初始化策略，知识库: {}, 文档数量: {}", 
                input.getKnowledgeBase().getName(), 
                input.getDocuments() != null ? input.getDocuments().size() : 0);
        
        InitializationResult result = new InitializationResult();
        
        try {
            // 1. 验证知识库信息
            if (input.getKnowledgeBase() == null) {
                throw new IllegalArgumentException("知识库信息不能为空");
            }
            
            // 2. 设置知识库属性
            if (enableMetadata) {
                result.setAttribute("metadataEnabled", true);
                logger.debug("启用元数据管理");
            }
            
            if (enableVectorization) {
                result.setAttribute("vectorizationEnabled", true);
                logger.debug("启用向量化处理");
            }
            
            // 3. 处理文档
            int processedCount = 0;
            if (input.getDocuments() != null && autoProcessDocuments) {
                processedCount = input.getDocuments().size();
                logger.info("准备处理 {} 个文档", processedCount);
                
                // 这里可以添加具体的文档处理逻辑
                // 例如：文档预处理、格式验证等
                for (int i = 0; i < processedCount; i += batchSize) {
                    int endIndex = Math.min(i + batchSize, processedCount);
                    logger.debug("处理文档批次: {}-{}", i + 1, endIndex);
                    // 批量处理逻辑
                }
            }
            
            // 4. 创建索引
            if (createIndexes) {
                result.setAttribute("indexesCreated", true);
                logger.debug("创建知识库索引");
            }
            
            // 5. 设置结果
            result.setSuccess(true);
            result.setMessage("知识库初始化成功");
            result.setProcessedDocuments(processedCount);
            result.setKnowledgeBaseId(input.getKnowledgeBase().getId());
            
            logger.info("默认初始化策略执行成功，处理文档数量: {}", processedCount);
            
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage("知识库初始化失败: " + e.getMessage());
            logger.error("默认初始化策略执行失败", e);
            throw e;
        }
        
        return result;
    }
}
