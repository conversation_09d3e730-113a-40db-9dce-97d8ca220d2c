<template>
  <div class="app-container">
    <div class="optimize-container">
      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span>知识库调优</span>
            <el-button type="primary" icon="Setting">配置调优</el-button>
          </div>
        </template>
        <div class="optimize-content">
          <el-empty description="知识库调优功能开发中..." />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup name="KnowledgeOptimize">
import { ref, onMounted } from 'vue'

// 响应式数据
const loading = ref(false)

// 生命周期
onMounted(() => {
  console.log('知识库调优页面已加载')
})
</script>

<style scoped>
.optimize-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.optimize-content {
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
