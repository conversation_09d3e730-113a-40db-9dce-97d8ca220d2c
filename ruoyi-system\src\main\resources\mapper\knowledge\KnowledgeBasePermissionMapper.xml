<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.knowledge.mapper.KnowledgeBasePermissionMapper">
    
    <resultMap type="KnowledgeBasePermission" id="KnowledgeBasePermissionResult">
        <result property="id"    column="id"    />
        <result property="knowledgeBaseId"    column="knowledge_base_id"    />
        <result property="knowledgeBaseName"    column="knowledge_base_name"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="nickName"    column="nick_name"    />
        <result property="permissionType"    column="permission_type"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectKnowledgeBasePermissionVo">
        select
            kbp.id,
            kbp.knowledge_base_id,
            kb.name as knowledge_base_name,
            kbp.user_id,
            u.user_name,
            u.nick_name,
            kbp.permission_type,
            kbp.create_by,
            kbp.create_time,
            kbp.update_by,
            kbp.update_time
        from knowledge_base_permission kbp
        left join knowledge_base kb on kbp.knowledge_base_id = kb.id
        left join sys_user u on kbp.user_id = u.user_id
    </sql>

    <select id="selectKnowledgeBasePermissionList" parameterType="KnowledgeBasePermission" resultMap="KnowledgeBasePermissionResult">
        <include refid="selectKnowledgeBasePermissionVo"/>
        <where>
            <if test="knowledgeBaseId != null "> and kbp.knowledge_base_id = #{knowledgeBaseId}</if>
            <if test="knowledgeBaseName != null  and knowledgeBaseName != ''"> and kb.name like concat('%', #{knowledgeBaseName}, '%')</if>
            <if test="userId != null "> and kbp.user_id = #{userId}</if>
            <if test="userName != null  and userName != ''"> and u.user_name like concat('%', #{userName}, '%')</if>
            <if test="nickName != null  and nickName != ''"> and u.nick_name like concat('%', #{nickName}, '%')</if>
            <if test="permissionType != null  and permissionType != ''"> and kbp.permission_type = #{permissionType}</if>
        </where>
        order by kbp.create_time desc
    </select>
    
    <select id="selectKnowledgeBasePermissionById" parameterType="Long" resultMap="KnowledgeBasePermissionResult">
        <include refid="selectKnowledgeBasePermissionVo"/>
        where kbp.id = #{id}
    </select>

    <select id="selectKnowledgeBasePermissionByUserId" parameterType="Long" resultMap="KnowledgeBasePermissionResult">
        <include refid="selectKnowledgeBasePermissionVo"/>
        where kbp.user_id = #{userId}
        order by kbp.create_time desc
    </select>

    <select id="selectKnowledgeBasePermissionByKnowledgeBaseId" parameterType="Long" resultMap="KnowledgeBasePermissionResult">
        <include refid="selectKnowledgeBasePermissionVo"/>
        where kbp.knowledge_base_id = #{knowledgeBaseId}
        order by kbp.create_time desc
    </select>

    <select id="selectUserKnowledgeBasePermission" resultMap="KnowledgeBasePermissionResult">
        <include refid="selectKnowledgeBasePermissionVo"/>
        where kbp.knowledge_base_id = #{knowledgeBaseId}
        and kbp.user_id = #{userId}
    </select>
        
    <insert id="insertKnowledgeBasePermission" parameterType="KnowledgeBasePermission" useGeneratedKeys="true" keyProperty="id">
        insert into knowledge_base_permission
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="knowledgeBaseId != null">knowledge_base_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="permissionType != null and permissionType != ''">permission_type,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="knowledgeBaseId != null">#{knowledgeBaseId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="permissionType != null and permissionType != ''">#{permissionType},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateKnowledgeBasePermission" parameterType="KnowledgeBasePermission">
        update knowledge_base_permission
        <trim prefix="SET " suffixOverrides=",">
            <if test="knowledgeBaseId != null">knowledge_base_id = #{knowledgeBaseId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="permissionType != null and permissionType != ''">permission_type = #{permissionType},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteKnowledgeBasePermissionById" parameterType="Long">
        delete from knowledge_base_permission where id = #{id}
    </delete>

    <delete id="deleteKnowledgeBasePermissionByIds" parameterType="String">
        delete from knowledge_base_permission where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteKnowledgeBasePermissionByKnowledgeBaseId" parameterType="Long">
        delete from knowledge_base_permission where knowledge_base_id = #{knowledgeBaseId}
    </delete>

    <delete id="deleteKnowledgeBasePermissionByUserId" parameterType="Long">
        delete from knowledge_base_permission where user_id = #{userId}
    </delete>

    <insert id="batchInsertKnowledgeBasePermission" parameterType="java.util.List">
        insert into knowledge_base_permission (knowledge_base_id, user_id, permission_type, create_by, create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.knowledgeBaseId}, #{item.userId}, #{item.permissionType}, #{item.createBy}, #{item.createTime})
        </foreach>
    </insert>
</mapper>
