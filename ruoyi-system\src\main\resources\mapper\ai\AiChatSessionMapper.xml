<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ai.mapper.AiChatSessionMapper">
    
    <resultMap type="AiChatSession" id="AiChatSessionResult">
        <result property="sessionId"    column="session_id"    />
        <result property="title"    column="title"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="model"    column="model"    />
        <result property="status"    column="status"    />
        <result property="lastActiveTime"    column="last_active_time"    />
        <result property="messageCount"    column="message_count"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectAiChatSessionVo">
        select session_id, title, user_id, user_name, model, status, last_active_time, message_count, create_by, create_time, update_by, update_time, remark from ai_chat_session
    </sql>

    <select id="selectAiChatSessionList" parameterType="AiChatSession" resultMap="AiChatSessionResult">
        <include refid="selectAiChatSessionVo"/>
        <where>  
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="model != null  and model != ''"> and model = #{model}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="lastActiveTime != null "> and last_active_time = #{lastActiveTime}</if>
        </where>
        order by last_active_time desc, create_time desc
    </select>
    
    <select id="selectAiChatSessionBySessionId" parameterType="String" resultMap="AiChatSessionResult">
        <include refid="selectAiChatSessionVo"/>
        where session_id = #{sessionId}
    </select>

    <select id="selectAiChatSessionListByUserId" parameterType="Long" resultMap="AiChatSessionResult">
        <include refid="selectAiChatSessionVo"/>
        where user_id = #{userId} and status = '0'
        order by last_active_time desc, create_time desc
    </select>
        
    <insert id="insertAiChatSession" parameterType="AiChatSession">
        insert into ai_chat_session
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sessionId != null">session_id,</if>
            <if test="title != null">title,</if>
            <if test="userId != null">user_id,</if>
            <if test="userName != null">user_name,</if>
            <if test="model != null">model,</if>
            <if test="status != null">status,</if>
            <if test="lastActiveTime != null">last_active_time,</if>
            <if test="messageCount != null">message_count,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sessionId != null">#{sessionId},</if>
            <if test="title != null">#{title},</if>
            <if test="userId != null">#{userId},</if>
            <if test="userName != null">#{userName},</if>
            <if test="model != null">#{model},</if>
            <if test="status != null">#{status},</if>
            <if test="lastActiveTime != null">#{lastActiveTime},</if>
            <if test="messageCount != null">#{messageCount},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateAiChatSession" parameterType="AiChatSession">
        update ai_chat_session
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null">title = #{title},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="model != null">model = #{model},</if>
            <if test="status != null">status = #{status},</if>
            <if test="lastActiveTime != null">last_active_time = #{lastActiveTime},</if>
            <if test="messageCount != null">message_count = #{messageCount},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where session_id = #{sessionId}
    </update>

    <delete id="deleteAiChatSessionBySessionId" parameterType="String">
        delete from ai_chat_session where session_id = #{sessionId}
    </delete>

    <delete id="deleteAiChatSessionBySessionIds" parameterType="String">
        delete from ai_chat_session where session_id in 
        <foreach item="sessionId" collection="array" open="(" separator="," close=")">
            #{sessionId}
        </foreach>
    </delete>

    <update id="updateLastActiveTime" parameterType="String">
        update ai_chat_session set last_active_time = now() where session_id = #{sessionId}
    </update>

    <update id="increaseMessageCount">
        update ai_chat_session set message_count = message_count + #{count} where session_id = #{sessionId}
    </update>

    <select id="checkSessionOwnership" resultType="int">
        select count(1) from ai_chat_session where session_id = #{sessionId} and user_id = #{userId}
    </select>

</mapper>
