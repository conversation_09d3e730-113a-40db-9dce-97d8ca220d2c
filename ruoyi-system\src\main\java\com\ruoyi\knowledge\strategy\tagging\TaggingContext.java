package com.ruoyi.knowledge.strategy.tagging;

import com.ruoyi.knowledge.domain.KnowledgeDocument;

/**
 * 标签策略上下文
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
public class TaggingContext {
    
    /** 知识库ID */
    private Long knowledgeBaseId;
    
    /** 文档对象 */
    private KnowledgeDocument document;
    
    /** 文档内容 */
    private String content;
    
    /** 文档标题 */
    private String title;
    
    /** 文档摘要 */
    private String summary;
    
    public TaggingContext() {}
    
    public TaggingContext(Long knowledgeBaseId, KnowledgeDocument document, String content) {
        this.knowledgeBaseId = knowledgeBaseId;
        this.document = document;
        this.content = content;
        this.title = document != null ? document.getTitle() : null;
        this.summary = document != null ? document.getSummary() : null;
    }
    
    public Long getKnowledgeBaseId() {
        return knowledgeBaseId;
    }
    
    public void setKnowledgeBaseId(Long knowledgeBaseId) {
        this.knowledgeBaseId = knowledgeBaseId;
    }
    
    public KnowledgeDocument getDocument() {
        return document;
    }
    
    public void setDocument(KnowledgeDocument document) {
        this.document = document;
    }
    
    public String getContent() {
        return content;
    }
    
    public void setContent(String content) {
        this.content = content;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getSummary() {
        return summary;
    }
    
    public void setSummary(String summary) {
        this.summary = summary;
    }
    
    @Override
    public String toString() {
        return "TaggingContext{" +
                "knowledgeBaseId=" + knowledgeBaseId +
                ", document=" + (document != null ? document.getName() : "null") +
                ", contentLength=" + (content != null ? content.length() : 0) +
                ", title='" + title + '\'' +
                ", summary='" + summary + '\'' +
                '}';
    }
}
