package com.ruoyi.knowledge.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 知识库文档对象 knowledge_document
 * 
 * <AUTHOR>
 * @date 2024-07-10
 */
public class KnowledgeDocument extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 文档ID */
    private Long id;

    /** 文档名称 */
    @Excel(name = "文档名称")
    private String name;

    /** 文档标题 */
    @Excel(name = "文档标题")
    private String title;

    /** 文档内容 */
    private String content;

    /** 文档摘要 */
    @Excel(name = "文档摘要")
    private String summary;

    /** 所属知识库ID */
    @Excel(name = "所属知识库ID")
    private Long knowledgeBaseId;

    /** 所属目录ID */
    @Excel(name = "所属目录ID")
    private Long folderId;

    /** 文档类型 */
    @Excel(name = "文档类型")
    private String type;

    /** 文档格式 */
    @Excel(name = "文档格式")
    private String format;

    /** 文档大小（字节） */
    @Excel(name = "文档大小")
    private Long size;

    /** 文档状态（0正常 1停用 2处理中） */
    @Excel(name = "文档状态", readConverterExp = "0=正常,1=停用,2=处理中")
    private String status;

    /** 处理状态（0未处理 1已处理 2处理失败） */
    @Excel(name = "处理状态", readConverterExp = "0=未处理,1=已处理,2=处理失败")
    private String processStatus;

    /** 文件路径 */
    @Excel(name = "文件路径")
    private String filePath;

    /** 文件URL */
    @Excel(name = "文件URL")
    private String fileUrl;

    /** 标签 */
    @Excel(name = "标签")
    private String tags;

    /** 版本号 */
    @Excel(name = "版本号")
    private String version;

    /** 是否公开（0私有 1公开） */
    @Excel(name = "是否公开", readConverterExp = "0=私有,1=公开")
    private String isPublic;

    /** 访问次数 */
    @Excel(name = "访问次数")
    private Long viewCount;

    /** 下载次数 */
    @Excel(name = "下载次数")
    private Long downloadCount;

    /** 最后访问时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最后访问时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date lastAccessTime;

    /** 创建者ID */
    @Excel(name = "创建者ID")
    private Long creatorId;

    /** 创建者名称 */
    @Excel(name = "创建者名称")
    private String creatorName;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }
    public void setTitle(String title) 
    {
        this.title = title;
    }

    public String getTitle() 
    {
        return title;
    }
    public void setContent(String content) 
    {
        this.content = content;
    }

    public String getContent() 
    {
        return content;
    }
    public void setSummary(String summary) 
    {
        this.summary = summary;
    }

    public String getSummary() 
    {
        return summary;
    }
    public void setKnowledgeBaseId(Long knowledgeBaseId) 
    {
        this.knowledgeBaseId = knowledgeBaseId;
    }

    public Long getKnowledgeBaseId() 
    {
        return knowledgeBaseId;
    }
    public void setFolderId(Long folderId) 
    {
        this.folderId = folderId;
    }

    public Long getFolderId() 
    {
        return folderId;
    }
    public void setType(String type) 
    {
        this.type = type;
    }

    public String getType() 
    {
        return type;
    }
    public void setFormat(String format) 
    {
        this.format = format;
    }

    public String getFormat() 
    {
        return format;
    }
    public void setSize(Long size) 
    {
        this.size = size;
    }

    public Long getSize() 
    {
        return size;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setProcessStatus(String processStatus) 
    {
        this.processStatus = processStatus;
    }

    public String getProcessStatus() 
    {
        return processStatus;
    }
    public void setFilePath(String filePath) 
    {
        this.filePath = filePath;
    }

    public String getFilePath() 
    {
        return filePath;
    }
    public void setFileUrl(String fileUrl) 
    {
        this.fileUrl = fileUrl;
    }

    public String getFileUrl() 
    {
        return fileUrl;
    }
    public void setTags(String tags) 
    {
        this.tags = tags;
    }

    public String getTags() 
    {
        return tags;
    }
    public void setVersion(String version) 
    {
        this.version = version;
    }

    public String getVersion() 
    {
        return version;
    }
    public void setIsPublic(String isPublic) 
    {
        this.isPublic = isPublic;
    }

    public String getIsPublic() 
    {
        return isPublic;
    }
    public void setViewCount(Long viewCount) 
    {
        this.viewCount = viewCount;
    }

    public Long getViewCount() 
    {
        return viewCount;
    }
    public void setDownloadCount(Long downloadCount) 
    {
        this.downloadCount = downloadCount;
    }

    public Long getDownloadCount() 
    {
        return downloadCount;
    }
    public void setLastAccessTime(Date lastAccessTime) 
    {
        this.lastAccessTime = lastAccessTime;
    }

    public Date getLastAccessTime() 
    {
        return lastAccessTime;
    }
    public void setCreatorId(Long creatorId) 
    {
        this.creatorId = creatorId;
    }

    public Long getCreatorId() 
    {
        return creatorId;
    }
    public void setCreatorName(String creatorName) 
    {
        this.creatorName = creatorName;
    }

    public String getCreatorName() 
    {
        return creatorName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("name", getName())
            .append("title", getTitle())
            .append("content", getContent())
            .append("summary", getSummary())
            .append("knowledgeBaseId", getKnowledgeBaseId())
            .append("folderId", getFolderId())
            .append("type", getType())
            .append("format", getFormat())
            .append("size", getSize())
            .append("status", getStatus())
            .append("processStatus", getProcessStatus())
            .append("filePath", getFilePath())
            .append("fileUrl", getFileUrl())
            .append("tags", getTags())
            .append("version", getVersion())
            .append("isPublic", getIsPublic())
            .append("viewCount", getViewCount())
            .append("downloadCount", getDownloadCount())
            .append("lastAccessTime", getLastAccessTime())
            .append("creatorId", getCreatorId())
            .append("creatorName", getCreatorName())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
