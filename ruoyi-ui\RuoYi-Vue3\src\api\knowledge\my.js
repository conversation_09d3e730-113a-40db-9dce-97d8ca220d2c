import request from '@/utils/request'

// 查询我的知识库列表（我创建的 + 我有权限的）
export function listMyKnowledgeBase(query) {
  return request({
    url: '/knowledge/my/list',
    method: 'get',
    params: query
  })
}

// 查询我创建的知识库列表
export function listCreatedKnowledgeBase(query) {
  return request({
    url: '/knowledge/my/created',
    method: 'get',
    params: query
  })
}

// 查询我有权限的知识库列表（不包括我创建的）
export function listSharedKnowledgeBase(query) {
  return request({
    url: '/knowledge/my/shared',
    method: 'get',
    params: query
  })
}

// 查询我的知识库详细
export function getMyKnowledgeBase(id) {
  return request({
    url: '/knowledge/my/' + id,
    method: 'get'
  })
}

// 新增我的知识库
export function addMyKnowledgeBase(data) {
  return request({
    url: '/knowledge/my',
    method: 'post',
    data: data
  })
}

// 修改我的知识库
export function updateMyKnowledgeBase(data) {
  return request({
    url: '/knowledge/my',
    method: 'put',
    data: data
  })
}

// 删除我的知识库
export function delMyKnowledgeBase(ids) {
  return request({
    url: '/knowledge/my/' + ids,
    method: 'delete'
  })
}

// 导出我的知识库
export function exportMyKnowledgeBase(query) {
  return request({
    url: '/knowledge/my/export',
    method: 'post',
    params: query
  })
}
