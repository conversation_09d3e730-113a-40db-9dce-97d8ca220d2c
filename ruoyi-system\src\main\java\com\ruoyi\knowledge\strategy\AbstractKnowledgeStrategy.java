package com.ruoyi.knowledge.strategy;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.knowledge.enums.StrategyType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 知识库策略抽象基类
 * 提供策略的通用功能实现
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
public abstract class AbstractKnowledgeStrategy<T, R> implements KnowledgeStrategy<T, R> {
    
    protected final Logger logger = LoggerFactory.getLogger(getClass());
    
    protected final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 解析配置JSON
     * 
     * @param config 配置JSON字符串
     * @return JsonNode对象
     */
    protected JsonNode parseConfig(String config) {
        try {
            if (config == null || config.trim().isEmpty()) {
                return objectMapper.readTree(getDefaultConfig());
            }
            return objectMapper.readTree(config);
        } catch (Exception e) {
            logger.warn("解析策略配置失败，使用默认配置: {}", e.getMessage());
            try {
                return objectMapper.readTree(getDefaultConfig());
            } catch (Exception ex) {
                logger.error("解析默认配置失败: {}", ex.getMessage());
                return objectMapper.createObjectNode();
            }
        }
    }
    
    /**
     * 获取配置项的字符串值
     * 
     * @param config 配置节点
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    protected String getConfigString(JsonNode config, String key, String defaultValue) {
        JsonNode node = config.get(key);
        return node != null ? node.asText(defaultValue) : defaultValue;
    }
    
    /**
     * 获取配置项的整数值
     * 
     * @param config 配置节点
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    protected int getConfigInt(JsonNode config, String key, int defaultValue) {
        JsonNode node = config.get(key);
        return node != null ? node.asInt(defaultValue) : defaultValue;
    }
    
    /**
     * 获取配置项的布尔值
     * 
     * @param config 配置节点
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    protected boolean getConfigBoolean(JsonNode config, String key, boolean defaultValue) {
        JsonNode node = config.get(key);
        return node != null ? node.asBoolean(defaultValue) : defaultValue;
    }
    
    /**
     * 获取配置项的双精度值
     * 
     * @param config 配置节点
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    protected double getConfigDouble(JsonNode config, String key, double defaultValue) {
        JsonNode node = config.get(key);
        return node != null ? node.asDouble(defaultValue) : defaultValue;
    }
    
    @Override
    public boolean validateConfig(String config) {
        try {
            if (config == null || config.trim().isEmpty()) {
                return true; // 空配置使用默认配置，认为有效
            }
            objectMapper.readTree(config);
            return true;
        } catch (Exception e) {
            logger.warn("策略配置验证失败: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 执行前的预处理
     * 
     * @param input 输入数据
     * @param config 配置
     */
    protected void preExecute(T input, String config) {
        logger.debug("开始执行策略: {} - {}", getStrategyType(), getStrategyName());
    }
    
    /**
     * 执行后的后处理
     * 
     * @param input 输入数据
     * @param result 执行结果
     * @param config 配置
     */
    protected void postExecute(T input, R result, String config) {
        logger.debug("策略执行完成: {} - {}", getStrategyType(), getStrategyName());
    }
    
    @Override
    public final R execute(T input, String config) throws Exception {
        preExecute(input, config);
        try {
            R result = doExecute(input, config);
            postExecute(input, result, config);
            return result;
        } catch (Exception e) {
            logger.error("策略执行失败: {} - {}, 错误: {}", getStrategyType(), getStrategyName(), e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 具体的策略执行逻辑，由子类实现
     * 
     * @param input 输入数据
     * @param config 策略配置
     * @return 执行结果
     * @throws Exception 执行异常
     */
    protected abstract R doExecute(T input, String config) throws Exception;
}
