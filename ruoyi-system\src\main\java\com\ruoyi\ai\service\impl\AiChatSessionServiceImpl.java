package com.ruoyi.ai.service.impl;

import java.util.Date;
import java.util.List;
import java.util.UUID;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.ai.mapper.AiChatSessionMapper;
import com.ruoyi.ai.domain.AiChatSession;
import com.ruoyi.ai.service.IAiChatSessionService;
import com.ruoyi.common.utils.SecurityUtils;

/**
 * AI聊天会话Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-09
 */
@Service
public class AiChatSessionServiceImpl implements IAiChatSessionService 
{
    @Autowired
    private AiChatSessionMapper aiChatSessionMapper;

    /**
     * 查询AI聊天会话
     * 
     * @param sessionId AI聊天会话主键
     * @return AI聊天会话
     */
    @Override
    public AiChatSession selectAiChatSessionBySessionId(String sessionId)
    {
        return aiChatSessionMapper.selectAiChatSessionBySessionId(sessionId);
    }

    /**
     * 查询AI聊天会话列表
     * 
     * @param aiChatSession AI聊天会话
     * @return AI聊天会话
     */
    @Override
    public List<AiChatSession> selectAiChatSessionList(AiChatSession aiChatSession)
    {
        return aiChatSessionMapper.selectAiChatSessionList(aiChatSession);
    }

    /**
     * 根据用户ID查询会话列表
     * 
     * @param userId 用户ID
     * @return AI聊天会话集合
     */
    @Override
    public List<AiChatSession> selectAiChatSessionListByUserId(Long userId)
    {
        return aiChatSessionMapper.selectAiChatSessionListByUserId(userId);
    }

    /**
     * 新增AI聊天会话
     * 
     * @param aiChatSession AI聊天会话
     * @return 结果
     */
    @Override
    public int insertAiChatSession(AiChatSession aiChatSession)
    {
        aiChatSession.setCreateTime(new Date());
        return aiChatSessionMapper.insertAiChatSession(aiChatSession);
    }

    /**
     * 修改AI聊天会话
     * 
     * @param aiChatSession AI聊天会话
     * @return 结果
     */
    @Override
    public int updateAiChatSession(AiChatSession aiChatSession)
    {
        aiChatSession.setUpdateTime(new Date());
        return aiChatSessionMapper.updateAiChatSession(aiChatSession);
    }

    /**
     * 批量删除AI聊天会话
     * 
     * @param sessionIds 需要删除的AI聊天会话主键
     * @return 结果
     */
    @Override
    public int deleteAiChatSessionBySessionIds(String[] sessionIds)
    {
        return aiChatSessionMapper.deleteAiChatSessionBySessionIds(sessionIds);
    }

    /**
     * 删除AI聊天会话信息
     * 
     * @param sessionId AI聊天会话主键
     * @return 结果
     */
    @Override
    public int deleteAiChatSessionBySessionId(String sessionId)
    {
        return aiChatSessionMapper.deleteAiChatSessionBySessionId(sessionId);
    }

    /**
     * 创建新会话
     * 
     * @param title 会话标题
     * @param model AI模型
     * @param userId 用户ID
     * @param userName 用户名
     * @param remark 备注
     * @return 会话ID
     */
    @Override
    public String createSession(String title, String model, Long userId, String userName, String remark)
    {
        String sessionId = UUID.randomUUID().toString().replace("-", "");
        
        AiChatSession session = new AiChatSession();
        session.setSessionId(sessionId);
        session.setTitle(title != null ? title : "新对话");
        session.setUserId(userId);
        session.setUserName(userName);
        session.setModel(model != null ? model : "qwen-plus");
        session.setStatus("0");
        session.setLastActiveTime(new Date());
        session.setMessageCount(0L);
        session.setCreateBy(userName);
        session.setCreateTime(new Date());
        session.setRemark(remark);
        
        aiChatSessionMapper.insertAiChatSession(session);
        return sessionId;
    }

    /**
     * 更新会话最后活跃时间
     * 
     * @param sessionId 会话ID
     * @return 结果
     */
    @Override
    public int updateLastActiveTime(String sessionId)
    {
        return aiChatSessionMapper.updateLastActiveTime(sessionId);
    }

    /**
     * 增加会话消息数量
     * 
     * @param sessionId 会话ID
     * @param count 增加数量
     * @return 结果
     */
    @Override
    public int increaseMessageCount(String sessionId, int count)
    {
        return aiChatSessionMapper.increaseMessageCount(sessionId, count);
    }

    /**
     * 检查会话是否属于用户
     * 
     * @param sessionId 会话ID
     * @param userId 用户ID
     * @return 是否属于用户
     */
    @Override
    public boolean checkSessionOwnership(String sessionId, Long userId)
    {
        return aiChatSessionMapper.checkSessionOwnership(sessionId, userId) > 0;
    }
}
