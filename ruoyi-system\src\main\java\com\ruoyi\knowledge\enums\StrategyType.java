package com.ruoyi.knowledge.enums;

/**
 * 知识库策略类型枚举
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
public enum StrategyType {
    
    /**
     * 初始化策略 - 控制知识库的创建和初始化过程
     */
    INITIALIZATION("INITIALIZATION", "初始化策略", "控制知识库的创建和初始化过程"),
    
    /**
     * 段落切分策略 - 控制文档如何分割成段落
     */
    SEGMENTATION("SEGMENTATION", "段落切分策略", "控制文档如何分割成段落"),
    
    /**
     * 标签策略 - 为知识段落自动生成标签
     */
    TAGGING("TAGGING", "标签策略", "为知识段落自动生成标签"),
    
    /**
     * 归纳总结策略 - 对知识内容进行归纳总结
     */
    SUMMARIZATION("SUMMARIZATION", "归纳总结策略", "对知识内容进行归纳总结"),
    
    /**
     * 关联策略 - 建立知识之间的关联关系
     */
    ASSOCIATION("ASSOCIATION", "关联策略", "建立知识之间的关联关系");

    private final String code;
    private final String name;
    private final String description;

    StrategyType(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取策略类型
     */
    public static StrategyType fromCode(String code) {
        for (StrategyType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown strategy type code: " + code);
    }

    /**
     * 检查代码是否有效
     */
    public static boolean isValidCode(String code) {
        for (StrategyType type : values()) {
            if (type.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }
}
