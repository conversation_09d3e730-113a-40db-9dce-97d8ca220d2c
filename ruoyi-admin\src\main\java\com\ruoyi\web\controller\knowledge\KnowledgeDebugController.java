package com.ruoyi.web.controller.knowledge;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.knowledge.domain.KnowledgeBase;
import com.ruoyi.knowledge.domain.KnowledgeDocument;
import com.ruoyi.knowledge.service.IKnowledgeBaseService;
import com.ruoyi.knowledge.service.IKnowledgeDocumentService;
import com.ruoyi.knowledge.service.IKnowledgeRagService;
import com.ruoyi.knowledge.service.IKnowledgeBasePermissionService;
import com.ruoyi.knowledge.domain.KnowledgeBasePermission;
import com.ruoyi.common.utils.SecurityUtils;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.store.embedding.EmbeddingStore;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 知识库调试控制器
 * 用于调试知识库相关问题
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
@RestController
@RequestMapping("/knowledge/debug")
public class KnowledgeDebugController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(KnowledgeDebugController.class);

    @Autowired
    private IKnowledgeBaseService knowledgeBaseService;

    @Autowired
    private IKnowledgeDocumentService knowledgeDocumentService;

    @Autowired
    private IKnowledgeRagService knowledgeRagService;

    @Autowired
    private IKnowledgeBasePermissionService knowledgeBasePermissionService;

    @Autowired
    private EmbeddingStore<TextSegment> embeddingStore;

    /**
     * 获取知识库调试信息
     */
    @GetMapping("/info/{knowledgeBaseId}")
    public AjaxResult getKnowledgeBaseDebugInfo(@PathVariable Long knowledgeBaseId) {
        try {
            Map<String, Object> debugInfo = new HashMap<>();

            // 1. 检查知识库是否存在
            KnowledgeBase knowledgeBase = knowledgeBaseService.selectKnowledgeBaseById(knowledgeBaseId);
            debugInfo.put("knowledgeBaseExists", knowledgeBase != null);
            if (knowledgeBase != null) {
                debugInfo.put("knowledgeBaseName", knowledgeBase.getName());
                debugInfo.put("knowledgeBaseStatus", knowledgeBase.getStatus());
                debugInfo.put("documentCount", knowledgeBase.getDocumentCount());
            }

            // 2. 检查知识库中的文档
            List<KnowledgeDocument> documents = knowledgeDocumentService.selectKnowledgeDocumentList(
                    new KnowledgeDocument() {
                        {
                            setKnowledgeBaseId(knowledgeBaseId);
                        }
                    });
            debugInfo.put("actualDocumentCount", documents.size());
            debugInfo.put("documents", documents);

            // 3. 测试知识库搜索
            String testQuery = "测试";
            List<String> searchResults = knowledgeRagService.searchInKnowledgeBase(knowledgeBaseId, testQuery, 5);
            debugInfo.put("testSearchQuery", testQuery);
            debugInfo.put("testSearchResults", searchResults);
            debugInfo.put("testSearchResultCount", searchResults.size());

            logger.info("知识库 {} 调试信息: {}", knowledgeBaseId, debugInfo);
            return AjaxResult.success(debugInfo);

        } catch (Exception e) {
            logger.error("获取知识库调试信息失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取调试信息失败: " + e.getMessage());
        }
    }

    /**
     * 测试知识库搜索
     */
    @PostMapping("/search/{knowledgeBaseId}")
    public AjaxResult testSearch(@PathVariable Long knowledgeBaseId, @RequestBody Map<String, String> request) {
        try {
            String query = request.get("query");
            if (query == null || query.trim().isEmpty()) {
                return AjaxResult.error("查询内容不能为空");
            }

            Map<String, Object> result = new HashMap<>();

            // 执行搜索
            List<String> searchResults = knowledgeRagService.searchInKnowledgeBase(knowledgeBaseId, query, 5);
            result.put("query", query);
            result.put("knowledgeBaseId", knowledgeBaseId);
            result.put("results", searchResults);
            result.put("resultCount", searchResults.size());

            logger.info("测试搜索完成 - 知识库: {}, 查询: {}, 结果数: {}", knowledgeBaseId, query, searchResults.size());
            return AjaxResult.success(result);

        } catch (Exception e) {
            logger.error("测试搜索失败: {}", e.getMessage(), e);
            return AjaxResult.error("测试搜索失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户有权限的知识库列表
     */
    @GetMapping("/list")
    public AjaxResult getAllKnowledgeBases() {
        try {
            Long currentUserId = SecurityUtils.getUserId();

            // 查询当前用户有权限的知识库
            List<KnowledgeBasePermission> permissions = knowledgeBasePermissionService.selectKnowledgeBasePermissionByUserId(currentUserId);

            List<KnowledgeBase> knowledgeBases;
            if (permissions.isEmpty()) {
                knowledgeBases = new ArrayList<>();
            } else {
                // 提取知识库ID列表
                Long[] knowledgeBaseIds = permissions.stream()
                    .map(KnowledgeBasePermission::getKnowledgeBaseId)
                    .toArray(Long[]::new);

                // 查询知识库详情
                KnowledgeBase queryParam = new KnowledgeBase();
                queryParam.setIds(knowledgeBaseIds);
                knowledgeBases = knowledgeBaseService.selectKnowledgeBaseList(queryParam);
            }

            Map<String, Object> result = new HashMap<>();
            result.put("knowledgeBases", knowledgeBases);
            result.put("totalCount", knowledgeBases.size());

            logger.info("获取知识库列表完成，共 {} 个知识库", knowledgeBases.size());
            return AjaxResult.success(result);

        } catch (Exception e) {
            logger.error("获取知识库列表失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取知识库列表失败: " + e.getMessage());
        }
    }

    /**
     * 将文档关联到知识库
     */
    @PostMapping("/associate/{documentId}/{knowledgeBaseId}")
    public AjaxResult associateDocumentToKnowledgeBase(@PathVariable Long documentId,
            @PathVariable Long knowledgeBaseId) {
        try {
            // 获取文档
            KnowledgeDocument document = knowledgeDocumentService.selectKnowledgeDocumentById(documentId);
            if (document == null) {
                return AjaxResult.error("文档不存在");
            }

            // 获取知识库
            KnowledgeBase knowledgeBase = knowledgeBaseService.selectKnowledgeBaseById(knowledgeBaseId);
            if (knowledgeBase == null) {
                return AjaxResult.error("知识库不存在");
            }

            // 更新文档的知识库关联
            document.setKnowledgeBaseId(knowledgeBaseId);
            document.setProcessStatus("0"); // 重置为未处理状态
            knowledgeDocumentService.updateKnowledgeDocument(document);

            Map<String, Object> result = new HashMap<>();
            result.put("documentId", documentId);
            result.put("documentName", document.getName());
            result.put("knowledgeBaseId", knowledgeBaseId);
            result.put("knowledgeBaseName", knowledgeBase.getName());

            logger.info("文档 {} 已关联到知识库 {}", document.getName(), knowledgeBase.getName());
            return AjaxResult.success("文档关联成功", result);

        } catch (Exception e) {
            logger.error("关联文档到知识库失败: {}", e.getMessage(), e);
            return AjaxResult.error("关联失败: " + e.getMessage());
        }
    }

    /**
     * 重建知识库索引
     */
    @PostMapping("/rebuild/{knowledgeBaseId}")
    public AjaxResult rebuildKnowledgeBase(@PathVariable Long knowledgeBaseId) {
        try {
            // 获取知识库信息
            KnowledgeBase knowledgeBase = knowledgeBaseService.selectKnowledgeBaseById(knowledgeBaseId);
            if (knowledgeBase == null) {
                return AjaxResult.error("知识库不存在");
            }

            // 获取知识库中的文档
            List<KnowledgeDocument> documents = knowledgeDocumentService.selectKnowledgeDocumentList(
                    new KnowledgeDocument() {
                        {
                            setKnowledgeBaseId(knowledgeBaseId);
                        }
                    });

            if (documents.isEmpty()) {
                return AjaxResult.error("知识库中没有文档");
            }

            // 重新处理每个文档
            int successCount = 0;
            for (KnowledgeDocument document : documents) {
                try {
                    boolean success = knowledgeRagService.addDocumentToKnowledgeBase(knowledgeBaseId, document);
                    if (success) {
                        successCount++;
                    }
                } catch (Exception e) {
                    logger.error("重建文档 {} 失败: {}", document.getName(), e.getMessage());
                }
            }

            Map<String, Object> result = new HashMap<>();
            result.put("knowledgeBaseId", knowledgeBaseId);
            result.put("totalDocuments", documents.size());
            result.put("successCount", successCount);
            result.put("failedCount", documents.size() - successCount);

            logger.info("知识库 {} 重建完成，成功: {}/{}", knowledgeBaseId, successCount, documents.size());
            return AjaxResult.success(result);

        } catch (Exception e) {
            logger.error("重建知识库失败: {}", e.getMessage(), e);
            return AjaxResult.error("重建知识库失败: " + e.getMessage());
        }
    }
}
