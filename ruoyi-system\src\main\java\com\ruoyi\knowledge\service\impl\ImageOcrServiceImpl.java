package com.ruoyi.knowledge.service.impl;

import com.ruoyi.knowledge.service.IImageOcrService;
import net.sourceforge.tess4j.ITesseract;
import net.sourceforge.tess4j.Tesseract;
import net.sourceforge.tess4j.TesseractException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.File;

/**
 * 图片OCR服务实现类
 * 使用Tesseract OCR引擎从图片中提取文本内容
 * 
 * <AUTHOR>
 * @date 2024-07-15
 */
@Service
public class ImageOcrServiceImpl implements IImageOcrService {

    private static final Logger logger = LoggerFactory.getLogger(ImageOcrServiceImpl.class);

    @Value("${ocr.tesseract.datapath:}")
    private String tesseractDataPath;

    @Value("${ocr.tesseract.language:chi_sim+eng}")
    private String tesseractLanguage;

    private ITesseract tesseract;
    private boolean ocrAvailable = false;

    @PostConstruct
    public void init() {
        try {
            tesseract = new Tesseract();

            // 设置Tesseract数据路径（如果配置了的话）
            if (tesseractDataPath != null && !tesseractDataPath.trim().isEmpty()) {
                tesseract.setDatapath(tesseractDataPath);
                logger.info("设置Tesseract数据路径: {}", tesseractDataPath);
            }

            // 设置识别语言（中文简体+英文）
            tesseract.setLanguage(tesseractLanguage);
            logger.info("设置Tesseract识别语言: {}", tesseractLanguage);

            // 设置OCR引擎模式（使用LSTM OCR引擎模式）
            tesseract.setOcrEngineMode(1);

            // 设置页面分割模式（自动页面分割，但不进行OSD）
            tesseract.setPageSegMode(3);

            ocrAvailable = true;
            logger.info("Tesseract OCR初始化成功");

        } catch (Exception e) {
            logger.error("Tesseract OCR初始化失败: {}", e.getMessage(), e);
            ocrAvailable = false;
        }
    }

    @Override
    public String extractTextFromImage(File imageFile) {
        if (!ocrAvailable) {
            logger.warn("OCR服务不可用，无法提取图片文本");
            return "";
        }

        if (imageFile == null || !imageFile.exists()) {
            logger.error("图片文件不存在: {}", imageFile);
            return "";
        }

        try {
            logger.info("开始从图片提取文本: {}", imageFile.getAbsolutePath());

            String result = tesseract.doOCR(imageFile);

            if (result != null) {
                result = result.trim();
                logger.info("成功提取文本，长度: {} 字符", result.length());

                // 记录提取的文本预览（前100个字符）
                if (result.length() > 0) {
                    String preview = result.length() > 100 ? result.substring(0, 100) + "..." : result;
                    logger.debug("提取的文本预览: {}", preview);
                }

                return result;
            } else {
                logger.warn("未能从图片中提取到文本内容");
                return "";
            }

        } catch (TesseractException e) {
            logger.error("OCR文本提取失败: {}", e.getMessage(), e);
            return "";
        } catch (Exception e) {
            logger.error("图片文本提取过程中发生异常: {}", e.getMessage(), e);
            return "";
        }
    }

    @Override
    public String extractTextFromImage(String imagePath) {
        if (imagePath == null || imagePath.trim().isEmpty()) {
            logger.error("图片路径为空");
            return "";
        }

        File imageFile = new File(imagePath);
        return extractTextFromImage(imageFile);
    }

    @Override
    public boolean isOcrAvailable() {
        return ocrAvailable;
    }
}
