package com.ruoyi.ai.service;

import java.util.List;
import com.ruoyi.ai.domain.AiChatSession;

/**
 * AI聊天会话Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-09
 */
public interface IAiChatSessionService 
{
    /**
     * 查询AI聊天会话
     * 
     * @param sessionId AI聊天会话主键
     * @return AI聊天会话
     */
    public AiChatSession selectAiChatSessionBySessionId(String sessionId);

    /**
     * 查询AI聊天会话列表
     * 
     * @param aiChatSession AI聊天会话
     * @return AI聊天会话集合
     */
    public List<AiChatSession> selectAiChatSessionList(AiChatSession aiChatSession);

    /**
     * 根据用户ID查询会话列表
     * 
     * @param userId 用户ID
     * @return AI聊天会话集合
     */
    public List<AiChatSession> selectAiChatSessionListByUserId(Long userId);

    /**
     * 新增AI聊天会话
     * 
     * @param aiChatSession AI聊天会话
     * @return 结果
     */
    public int insertAiChatSession(AiChatSession aiChatSession);

    /**
     * 修改AI聊天会话
     * 
     * @param aiChatSession AI聊天会话
     * @return 结果
     */
    public int updateAiChatSession(AiChatSession aiChatSession);

    /**
     * 批量删除AI聊天会话
     * 
     * @param sessionIds 需要删除的AI聊天会话主键集合
     * @return 结果
     */
    public int deleteAiChatSessionBySessionIds(String[] sessionIds);

    /**
     * 删除AI聊天会话信息
     * 
     * @param sessionId AI聊天会话主键
     * @return 结果
     */
    public int deleteAiChatSessionBySessionId(String sessionId);

    /**
     * 创建新会话
     * 
     * @param title 会话标题
     * @param model AI模型
     * @param userId 用户ID
     * @param userName 用户名
     * @param remark 备注
     * @return 会话ID
     */
    public String createSession(String title, String model, Long userId, String userName, String remark);

    /**
     * 更新会话最后活跃时间
     * 
     * @param sessionId 会话ID
     * @return 结果
     */
    public int updateLastActiveTime(String sessionId);

    /**
     * 增加会话消息数量
     * 
     * @param sessionId 会话ID
     * @param count 增加数量
     * @return 结果
     */
    public int increaseMessageCount(String sessionId, int count);

    /**
     * 检查会话是否属于用户
     * 
     * @param sessionId 会话ID
     * @param userId 用户ID
     * @return 是否属于用户
     */
    public boolean checkSessionOwnership(String sessionId, Long userId);
}
