/**
 * 无障碍访问修复样式
 * 修复Element Plus组件的无障碍访问警告
 */

/* 修复aria-hidden警告 - 通用修复 */
.el-popper[aria-hidden="true"] {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  pointer-events: none !important;
}

/* 确保隐藏元素的子元素也不可访问 */
.el-popper[aria-hidden="true"] * {
  pointer-events: none !important;
  visibility: hidden !important;
}

/* 修复菜单组件的焦点问题 */
.el-menu {
  /* 子菜单弹出层 */
  .el-sub-menu__popup {
    &[aria-hidden="true"] {
      display: none !important;
    }
  }
  
  /* 菜单项焦点样式 */
  .el-menu-item {
    &:focus-visible {
      outline: 2px solid var(--el-color-primary);
      outline-offset: -2px;
    }
    
    &.is-active:focus {
      outline: none;
    }
  }
  
  /* 子菜单标题 */
  .el-sub-menu__title {
    &:focus-visible {
      outline: 2px solid var(--el-color-primary);
      outline-offset: -2px;
    }
  }
}

/* 修复下拉菜单的无障碍问题 */
.el-dropdown-menu {
  &[aria-hidden="true"] {
    display: none !important;
  }
}

/* 修复弹出框的无障碍问题 */
.el-popover {
  &[aria-hidden="true"] {
    display: none !important;
  }
}

/* 修复工具提示的无障碍问题 */
.el-tooltip__popper {
  &[aria-hidden="true"] {
    display: none !important;
  }
}

/* 修复选择器下拉的无障碍问题 */
.el-select-dropdown {
  &[aria-hidden="true"] {
    display: none !important;
  }
}

/* 修复日期选择器的无障碍问题 */
.el-picker-panel {
  &[aria-hidden="true"] {
    display: none !important;
  }
}

/* 通用的隐藏元素修复 */
[aria-hidden="true"] {
  &.el-popper,
  &.el-dropdown-menu,
  &.el-popover,
  &.el-tooltip__popper,
  &.el-select-dropdown,
  &.el-picker-panel {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    pointer-events: none !important;
  }
}

/* 确保过渡动画期间也正确处理 */
.el-zoom-in-left-leave-active,
.el-zoom-in-left-leave-to,
.el-fade-in-linear-leave-active,
.el-fade-in-linear-leave-to {
  &[aria-hidden="true"] {
    display: none !important;
  }
}

/* 修复移动端菜单的无障碍问题 */
@media (max-width: 992px) {
  .mobile .sidebar-container {
    .el-menu {
      .el-popper[aria-hidden="true"] {
        display: none !important;
      }
    }
  }
}
