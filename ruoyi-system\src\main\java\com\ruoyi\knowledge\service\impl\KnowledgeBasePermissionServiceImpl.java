package com.ruoyi.knowledge.service.impl;

import java.util.List;
import java.util.ArrayList;
import java.util.Date;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.knowledge.mapper.KnowledgeBasePermissionMapper;
import com.ruoyi.knowledge.domain.KnowledgeBasePermission;
import com.ruoyi.knowledge.service.IKnowledgeBasePermissionService;
import com.ruoyi.common.utils.SecurityUtils;

/**
 * 知识库权限Service业务层处理（简化版）
 * 
 * <AUTHOR>
 * @date 2025-08-22
 */
@Service
public class KnowledgeBasePermissionServiceImpl implements IKnowledgeBasePermissionService 
{
    @Autowired
    private KnowledgeBasePermissionMapper knowledgeBasePermissionMapper;

    /**
     * 查询知识库权限
     * 
     * @param id 知识库权限主键
     * @return 知识库权限
     */
    @Override
    public KnowledgeBasePermission selectKnowledgeBasePermissionById(Long id)
    {
        return knowledgeBasePermissionMapper.selectKnowledgeBasePermissionById(id);
    }

    /**
     * 查询知识库权限列表
     * 
     * @param knowledgeBasePermission 知识库权限
     * @return 知识库权限
     */
    @Override
    public List<KnowledgeBasePermission> selectKnowledgeBasePermissionList(KnowledgeBasePermission knowledgeBasePermission)
    {
        return knowledgeBasePermissionMapper.selectKnowledgeBasePermissionList(knowledgeBasePermission);
    }

    /**
     * 根据用户ID查询知识库权限列表
     * 
     * @param userId 用户ID
     * @return 知识库权限集合
     */
    @Override
    public List<KnowledgeBasePermission> selectKnowledgeBasePermissionByUserId(Long userId)
    {
        return knowledgeBasePermissionMapper.selectKnowledgeBasePermissionByUserId(userId);
    }

    /**
     * 根据知识库ID查询权限列表
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 知识库权限集合
     */
    @Override
    public List<KnowledgeBasePermission> selectKnowledgeBasePermissionByKnowledgeBaseId(Long knowledgeBaseId)
    {
        return knowledgeBasePermissionMapper.selectKnowledgeBasePermissionByKnowledgeBaseId(knowledgeBaseId);
    }

    /**
     * 检查用户对知识库的权限
     * 
     * @param knowledgeBaseId 知识库ID
     * @param userId 用户ID
     * @return 知识库权限
     */
    @Override
    public KnowledgeBasePermission selectUserKnowledgeBasePermission(Long knowledgeBaseId, Long userId)
    {
        return knowledgeBasePermissionMapper.selectUserKnowledgeBasePermission(knowledgeBaseId, userId);
    }

    /**
     * 检查用户是否有知识库的指定权限
     * 
     * @param knowledgeBaseId 知识库ID
     * @param userId 用户ID
     * @param requiredPermission 需要的权限类型
     * @return 是否有权限
     */
    @Override
    public boolean hasKnowledgeBasePermission(Long knowledgeBaseId, Long userId, String requiredPermission)
    {
        KnowledgeBasePermission permission = selectUserKnowledgeBasePermission(knowledgeBaseId, userId);
        if (permission == null) {
            return false;
        }
        
        String userPermission = permission.getPermissionType();
        
        // 权限层级：admin > write > read
        switch (requiredPermission) {
            case "read":
                return "read".equals(userPermission) || "write".equals(userPermission) || "admin".equals(userPermission);
            case "write":
                return "write".equals(userPermission) || "admin".equals(userPermission);
            case "admin":
                return "admin".equals(userPermission);
            default:
                return false;
        }
    }

    /**
     * 新增知识库权限
     * 
     * @param knowledgeBasePermission 知识库权限
     * @return 结果
     */
    @Override
    public int insertKnowledgeBasePermission(KnowledgeBasePermission knowledgeBasePermission)
    {
        knowledgeBasePermission.setCreateBy(SecurityUtils.getUsername());
        knowledgeBasePermission.setCreateTime(new Date());
        return knowledgeBasePermissionMapper.insertKnowledgeBasePermission(knowledgeBasePermission);
    }

    /**
     * 修改知识库权限
     * 
     * @param knowledgeBasePermission 知识库权限
     * @return 结果
     */
    @Override
    public int updateKnowledgeBasePermission(KnowledgeBasePermission knowledgeBasePermission)
    {
        knowledgeBasePermission.setUpdateBy(SecurityUtils.getUsername());
        knowledgeBasePermission.setUpdateTime(new Date());
        return knowledgeBasePermissionMapper.updateKnowledgeBasePermission(knowledgeBasePermission);
    }

    /**
     * 批量删除知识库权限
     * 
     * @param ids 需要删除的知识库权限主键
     * @return 结果
     */
    @Override
    public int deleteKnowledgeBasePermissionByIds(Long[] ids)
    {
        return knowledgeBasePermissionMapper.deleteKnowledgeBasePermissionByIds(ids);
    }

    /**
     * 删除知识库权限信息
     * 
     * @param id 知识库权限主键
     * @return 结果
     */
    @Override
    public int deleteKnowledgeBasePermissionById(Long id)
    {
        return knowledgeBasePermissionMapper.deleteKnowledgeBasePermissionById(id);
    }

    /**
     * 根据知识库ID删除权限
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 结果
     */
    @Override
    public int deleteKnowledgeBasePermissionByKnowledgeBaseId(Long knowledgeBaseId)
    {
        return knowledgeBasePermissionMapper.deleteKnowledgeBasePermissionByKnowledgeBaseId(knowledgeBaseId);
    }

    /**
     * 根据用户ID删除权限
     * 
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public int deleteKnowledgeBasePermissionByUserId(Long userId)
    {
        return knowledgeBasePermissionMapper.deleteKnowledgeBasePermissionByUserId(userId);
    }

    /**
     * 批量授权
     * 
     * @param knowledgeBaseId 知识库ID
     * @param userIds 用户ID列表
     * @param permissionType 权限类型
     * @param grantedBy 授权人ID（简化版中不使用）
     * @param grantedByName 授权人名称（简化版中不使用）
     * @return 结果
     */
    @Override
    public int batchGrantPermissions(Long knowledgeBaseId, Long[] userIds, String permissionType, Long grantedBy, String grantedByName)
    {
        List<KnowledgeBasePermission> permissions = new ArrayList<>();
        String currentUser = SecurityUtils.getUsername();
        Date now = new Date();
        
        for (Long userId : userIds) {
            // 检查是否已存在权限
            KnowledgeBasePermission existingPermission = selectUserKnowledgeBasePermission(knowledgeBaseId, userId);
            if (existingPermission != null) {
                // 更新现有权限
                existingPermission.setPermissionType(permissionType);
                existingPermission.setUpdateBy(currentUser);
                existingPermission.setUpdateTime(now);
                updateKnowledgeBasePermission(existingPermission);
            } else {
                // 创建新权限
                KnowledgeBasePermission permission = new KnowledgeBasePermission();
                permission.setKnowledgeBaseId(knowledgeBaseId);
                permission.setUserId(userId);
                permission.setPermissionType(permissionType);
                permission.setCreateBy(currentUser);
                permission.setCreateTime(now);
                permissions.add(permission);
            }
        }
        
        if (!permissions.isEmpty()) {
            return knowledgeBasePermissionMapper.batchInsertKnowledgeBasePermission(permissions);
        }
        
        return userIds.length; // 返回处理的用户数量
    }

    /**
     * 为知识库创建者自动授权管理员权限
     * 
     * @param knowledgeBaseId 知识库ID
     * @param creatorId 创建者ID
     * @param creatorName 创建者名称（简化版中不使用）
     * @return 结果
     */
    @Override
    public int grantCreatorAdminPermission(Long knowledgeBaseId, Long creatorId, String creatorName)
    {
        // 检查是否已存在权限
        KnowledgeBasePermission existingPermission = selectUserKnowledgeBasePermission(knowledgeBaseId, creatorId);
        if (existingPermission != null) {
            return 1; // 已存在权限，无需重复授权
        }
        
        KnowledgeBasePermission permission = new KnowledgeBasePermission();
        permission.setKnowledgeBaseId(knowledgeBaseId);
        permission.setUserId(creatorId);
        permission.setPermissionType("admin");
        permission.setCreateBy("system");
        permission.setCreateTime(new Date());
        
        return insertKnowledgeBasePermission(permission);
    }
}
