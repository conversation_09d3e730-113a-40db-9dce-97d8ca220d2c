<template>
  <div class="app-container home">
    <!-- 欢迎横幅 -->
    <el-row :gutter="20">
      <el-col :sm="24" :lg="24">
        <div class="welcome-banner">
          <div class="banner-content">
            <h1 class="banner-title">
              <el-icon class="banner-icon"><Document /></el-icon>
              智能知识管理系统
            </h1>
            <p class="banner-subtitle">
              基于大模型技术的企业级知识管理平台，让知识更智能、更高效
            </p>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 快速操作区域 -->
    <el-row :gutter="20" class="quick-actions">
      <el-col :sm="24" :lg="8">
        <el-card class="action-card" shadow="hover" @click="handleQuickAction('create')">
          <div class="action-content">
            <el-icon class="action-icon create"><Plus /></el-icon>
            <div class="action-text">
              <h3>创建知识库</h3>
              <p>快速创建新的知识库，开始知识管理</p>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :sm="24" :lg="8">
        <el-card class="action-card" shadow="hover" @click="handleQuickAction('upload')">
          <div class="action-content">
            <el-icon class="action-icon upload"><Upload /></el-icon>
            <div class="action-text">
              <h3>上传文档</h3>
              <p>批量上传文档，自动解析知识内容</p>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :sm="24" :lg="8">
        <el-card class="action-card" shadow="hover" @click="handleQuickAction('search')">
          <div class="action-content">
            <el-icon class="action-icon search"><Search /></el-icon>
            <div class="action-text">
              <h3>智能问答</h3>
              <p>基于AI的智能知识问答服务</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 系统概览 -->
    <el-row :gutter="20">
      <el-col :sm="24" :lg="12">
        <el-card class="overview-card">
          <template #header>
            <div class="card-header">
              <span>系统介绍</span>
            </div>
          </template>
          <div class="system-intro">
            <h3>智能知识管理系统</h3>
            <p>
              基于大模型技术构建的企业级知识管理平台，支持多种知识类型的统一管理，
              包括产品文档、技术手册、客服FAQ、API文档等。通过智能化手段实现知识的
              提炼、推荐和调优，提升企业知识管理效率。
            </p>
            <div class="features">
              <el-tag class="feature-tag" type="success">智能问答</el-tag>
              <el-tag class="feature-tag" type="info">语义检索</el-tag>
              <el-tag class="feature-tag" type="warning">自动优化</el-tag>
              <el-tag class="feature-tag" type="info">多模态支持</el-tag>
            </div>
            <p class="version-info">
              <b>当前版本:</b> <span>v{{ version }}</span>
            </p>
          </div>
        </el-card>
      </el-col>

      <el-col :sm="24" :lg="12">
        <el-card class="overview-card">
          <template #header>
            <div class="card-header">
              <span>核心特性</span>
            </div>
          </template>
          <div class="features-list">
            <div class="feature-item">
              <el-icon class="feature-icon"><Histogram /></el-icon>
              <div class="feature-content">
                <h4>多领域知识库</h4>
                <p>支持按领域分类管理，权限控制精确到知识库级别</p>
              </div>
            </div>
            <div class="feature-item">
              <el-icon class="feature-icon"><Brush /></el-icon>
              <div class="feature-content">
                <h4>智能初始化</h4>
                <p>提供多种知识初始化策略，支持自定义策略配置</p>
              </div>
            </div>
            <div class="feature-item">
              <el-icon class="feature-icon"><ChatDotRound /></el-icon>
              <div class="feature-content">
                <h4>自然语言问答</h4>
                <p>智能理解用户问题，提供精准答案并支持来源追溯</p>
              </div>
            </div>
            <div class="feature-item">
              <el-icon class="feature-icon"><TrendCharts /></el-icon>
              <div class="feature-content">
                <h4>自适应优化</h4>
                <p>持续优化知识内容和检索策略</p>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 技术架构 -->
    <el-row :gutter="20">
      <el-col :sm="24" :lg="12">
        <el-card class="tech-card">
          <template #header>
            <div class="card-header">
              <span>技术架构</span>
            </div>
          </template>
          <div class="tech-stack">
            <div class="tech-section">
              <h4><el-icon><Monitor /></el-icon> 后端技术</h4>
              <div class="tech-tags">
                <el-tag class="tech-tag">Spring Boot</el-tag>
                <el-tag class="tech-tag">Spring Security</el-tag>
                <el-tag class="tech-tag">MyBatis-Plus</el-tag>
                <el-tag class="tech-tag">Redis</el-tag>
                <el-tag class="tech-tag">Elasticsearch</el-tag>
                <el-tag class="tech-tag">MySQL</el-tag>
                <el-tag class="tech-tag">大模型API</el-tag>
              </div>
            </div>
            <div class="tech-section">
              <h4><el-icon><Monitor /></el-icon> 前端技术</h4>
              <div class="tech-tags">
                <el-tag class="tech-tag" type="success">Vue 3</el-tag>
                <el-tag class="tech-tag" type="success">Element Plus</el-tag>
                <el-tag class="tech-tag" type="success">TypeScript</el-tag>
                <el-tag class="tech-tag" type="success">Vite</el-tag>
                <el-tag class="tech-tag" type="success">Pinia</el-tag>
                <el-tag class="tech-tag" type="success">Axios</el-tag>
              </div>
            </div>
            <div class="tech-section">
              <h4><el-icon><Monitor /></el-icon> AI技术</h4>
              <div class="tech-tags">
                <el-tag class="tech-tag" type="warning">向量数据库</el-tag>
                <el-tag class="tech-tag" type="warning">语义检索</el-tag>
                <el-tag class="tech-tag" type="warning">文档解析</el-tag>
                <el-tag class="tech-tag" type="warning">知识图谱</el-tag>
                <el-tag class="tech-tag" type="warning">RAG架构</el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :sm="24" :lg="12">
        <el-card class="info-card">
          <template #header>
            <div class="card-header">
              <span>系统功能模块</span>
            </div>
          </template>
          <div class="module-info">
            <div class="module-item">
              <el-icon class="module-icon"><Document /></el-icon>
              <div class="module-content">
                <h4>知识库管理</h4>
                <p>创建、管理多领域知识库，支持权限控制</p>
              </div>
            </div>
            <div class="module-item">
              <el-icon class="module-icon"><ChatDotRound /></el-icon>
              <div class="module-content">
                <h4>智能问答</h4>
                <p>基于语义的知识库问答，支持来源追溯</p>
              </div>
            </div>
            <div class="module-item">
              <el-icon class="module-icon"><Upload /></el-icon>
              <div class="module-content">
                <h4>文档处理</h4>
                <p>支持多种格式文档解析和知识提取</p>
              </div>
            </div>
            <div class="module-item">
              <el-icon class="module-icon"><TrendCharts /></el-icon>
              <div class="module-content">
                <h4>知识优化</h4>
                <p>自动知识库优化</p>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-divider />

    <!-- 系统统计 -->
    <el-row :gutter="20">
      <el-col :xs="24" :sm="24" :md="12" :lg="8">
        <el-card class="info-card">
          <template #header>
            <div class="card-header">
              <span>用户角色说明</span>
            </div>
          </template>
          <div class="role-info">
            <div class="role-item">
              <el-icon class="role-icon super-admin"><Setting /></el-icon>
              <div class="role-content">
                <h4>超级管理员</h4>
                <p>拥有最高权限，负责用户管理和知识库所有人转移</p>
              </div>
            </div>
            <div class="role-item">
              <el-icon class="role-icon owner"><User /></el-icon>
              <div class="role-content">
                <h4>知识库所有人</h4>
                <p>创建知识库的用户，拥有该知识库的完全管理权限</p>
              </div>
            </div>
            <div class="role-item">
              <el-icon class="role-icon admin"><UserFilled /></el-icon>
              <div class="role-content">
                <h4>知识库管理员</h4>
                <p>可管理知识库权限、策略配置和知识调优</p>
              </div>
            </div>
            <div class="role-item">
              <el-icon class="role-icon user"><Avatar /></el-icon>
              <div class="role-content">
                <h4>普通用户</h4>
                <p>拥有基本使用权限，可创建知识库成为管理员</p>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="8">
        <el-card class="update-log">
          <template #header>
            <div class="card-header">
              <span>系统更新日志</span>
            </div>
          </template>
          <el-collapse accordion>
            <el-collapse-item title="v1.0.0 - 2024-01-15">
              <ol>
                <li>智能知识管理系统正式发布</li>
                <li>支持多种文档格式解析（PDF、Word、Excel、PPT等）</li>
                <li>集成大模型API，实现智能问答功能</li>
                <li>支持向量检索和语义搜索</li>
                <li>实现知识库权限管理体系</li>
                <li>支持自定义知识初始化策略</li>
                <li>提供知识库评测和优化建议</li>
                <li>支持跨库知识检索</li>

                <li>支持知识来源追溯</li>
                <li>提供丰富的管理后台功能</li>
                <li>支持多角色用户管理</li>
                <li>实现知识自动标签和分类</li>
                <li>支持知识关联推荐</li>
                <li>提供API接口供第三方集成</li>
              </ol>
            </el-collapse-item>
          </el-collapse>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="8">
        <el-card class="help-card">
          <template #header>
            <div class="card-header">
              <span>帮助与支持</span>
            </div>
          </template>
          <div class="help-content">
            <div class="help-item">
              <el-icon class="help-icon"><QuestionFilled /></el-icon>
              <div class="help-text">
                <h4>使用指南</h4>
                <p>查看详细的系统使用说明和最佳实践</p>
                <el-button text size="small" @click="openHelp('guide')">查看指南</el-button>
              </div>
            </div>
            <div class="help-item">
              <el-icon class="help-icon"><Tools /></el-icon>
              <div class="help-text">
                <h4>API文档</h4>
                <p>开发者接口文档和集成说明</p>
                <el-button text size="small" @click="openHelp('api')">查看文档</el-button>
              </div>
            </div>
            <div class="help-item">
              <el-icon class="help-icon"><ChatSquare /></el-icon>
              <div class="help-text">
                <h4>技术支持</h4>
                <p>遇到问题？联系我们的技术支持团队</p>
                <el-button text size="small" @click="openHelp('support')">获取支持</el-button>
              </div>
            </div>

          </div>
        </el-card>
      </el-col>
    </el-row>


  </div>
</template>

<script setup name="Index">
import { ref, onMounted } from 'vue'
import {
  Document, Plus, Upload, Search, Histogram, Brush,
  ChatDotRound, TrendCharts, Monitor, Setting, User, UserFilled,
  Avatar, QuestionFilled, Tools, ChatSquare, Star
} from '@element-plus/icons-vue'

const version = ref('1.0.0')









// 快速操作处理
const handleQuickAction = (action) => {
  switch (action) {
    case 'create':
      // 创建知识库功能已禁用
      console.log('创建知识库功能已禁用')
      break
    case 'upload':
      // 跳转到文档上传页面
      console.log('上传文档')
      break
    case 'search':
      // 跳转到智能问答页面
      console.log('智能问答')
      break
  }
}







// 打开帮助
const openHelp = (type) => {
  console.log('打开帮助:', type)
}

// 组件挂载时获取统计数据
onMounted(() => {
  // 这里可以调用API获取真实的统计数据
  console.log('页面已加载')
})
</script>

<style scoped lang="scss">
.home {
  font-family: "open sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 13px;
  color: #676a6c;
  overflow-x: hidden;

  // 欢迎横幅样式
  .welcome-banner {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: 40px;
    color: white;
    text-align: center;
    margin-bottom: 30px;

    .banner-title {
      font-size: 32px;
      font-weight: 600;
      margin-bottom: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12px;

      .banner-icon {
        font-size: 36px;
      }
    }

    .banner-subtitle {
      font-size: 18px;
      margin-bottom: 30px;
      opacity: 0.9;
    }


  }

  // 快速操作卡片样式
  .quick-actions {
    margin-bottom: 30px;

    .action-card {
      cursor: pointer;
      transition: all 0.3s ease;
      border: 2px solid transparent;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        border-color: #409eff;
      }

      .action-content {
        display: flex;
        align-items: center;
        padding: 20px;

        .action-icon {
          font-size: 48px;
          margin-right: 20px;

          &.create { color: #67c23a; }
          &.upload { color: #e6a23c; }
          &.search { color: #409eff; }
        }

        .action-text {
          h3 {
            margin: 0 0 8px 0;
            font-size: 18px;
            color: #303133;
          }

          p {
            margin: 0;
            color: #606266;
            font-size: 14px;
          }
        }
      }
    }
  }

  // 概览卡片样式
  .overview-card {
    margin-bottom: 20px;
    height: 100%;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 600;
    }

    .system-intro {
      h3 {
        color: #303133;
        margin-bottom: 16px;
      }

      p {
        line-height: 1.6;
        color: #606266;
        margin-bottom: 20px;
      }

      .features {
        margin-bottom: 20px;

        .feature-tag {
          margin-right: 8px;
          margin-bottom: 8px;
        }
      }

      .version-info {
        color: #909399;
        font-size: 14px;
      }
    }

    .features-list {
      .feature-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 20px;
        padding: 16px;
        border-radius: 8px;
        background: #f8f9fa;

        .feature-icon {
          font-size: 24px;
          color: #409eff;
          margin-right: 16px;
          margin-top: 4px;
        }

        .feature-content {
          h4 {
            margin: 0 0 8px 0;
            color: #303133;
            font-size: 16px;
          }

          p {
            margin: 0;
            color: #606266;
            font-size: 14px;
            line-height: 1.5;
          }
        }
      }
    }
  }

  // 技术架构卡片样式
  .tech-card {
    margin-bottom: 20px;

    .tech-stack {
      .tech-section {
        margin-bottom: 24px;

        h4 {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 12px;
          color: #303133;
          font-size: 16px;
        }

        .tech-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;

          .tech-tag {
            margin: 0;
          }
        }
      }
    }
  }

  // 模块信息样式
  .module-info {
    .module-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: 20px;
      padding: 16px;
      border-radius: 8px;
      background: #f8f9fa;

      .module-icon {
        font-size: 24px;
        color: #409eff;
        margin-right: 16px;
        margin-top: 4px;
      }

      .module-content {
        h4 {
          margin: 0 0 8px 0;
          color: #303133;
          font-size: 16px;
        }

        p {
          margin: 0;
          color: #606266;
          font-size: 14px;
          line-height: 1.5;
        }
      }
    }
  }

  // 信息卡片样式
  .info-card {
    margin-bottom: 20px;

    .role-info {
      .role-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 20px;
        padding: 16px;
        border-radius: 8px;
        background: #fafafa;

        .role-icon {
          font-size: 24px;
          margin-right: 16px;
          margin-top: 4px;

          &.super-admin { color: #f56c6c; }
          &.owner { color: #e6a23c; }
          &.admin { color: #409eff; }
          &.user { color: #67c23a; }
        }

        .role-content {
          h4 {
            margin: 0 0 8px 0;
            color: #303133;
            font-size: 16px;
          }

          p {
            margin: 0;
            color: #606266;
            font-size: 14px;
            line-height: 1.5;
          }
        }
      }
    }
  }

  // 帮助卡片样式
  .help-card {
    margin-bottom: 20px;

    .help-content {
      .help-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 20px;
        padding: 16px;
        border-radius: 8px;
        background: #f8f9fa;

        .help-icon {
          font-size: 24px;
          color: #909399;
          margin-right: 16px;
          margin-top: 4px;
        }

        .help-text {
          flex: 1;

          h4 {
            margin: 0 0 8px 0;
            color: #303133;
            font-size: 16px;
          }

          p {
            margin: 0 0 8px 0;
            color: #606266;
            font-size: 14px;
            line-height: 1.5;
          }
        }
      }
    }
  }

  // 更新日志样式
  .update-log {
    margin-bottom: 20px;

    ol {
      display: block;
      list-style-type: decimal;
      margin-block-start: 1em;
      margin-block-end: 1em;
      margin-inline-start: 0;
      margin-inline-end: 0;
      padding-inline-start: 40px;
    }
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
  }

  ul {
    padding: 0;
    margin: 0;
    list-style-type: none;
  }

  h4 {
    margin-top: 0px;
  }

  h2 {
    margin-top: 10px;
    font-size: 26px;
    font-weight: 100;
  }

  p {
    margin-top: 10px;

    b {
      font-weight: 700;
    }
  }


}
</style>