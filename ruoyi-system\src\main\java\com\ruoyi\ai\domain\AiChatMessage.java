package com.ruoyi.ai.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * AI聊天消息对象 ai_chat_message
 * 
 * <AUTHOR>
 * @date 2024-07-09
 */
public class AiChatMessage extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 消息ID */
    private String messageId;

    /** 会话ID */
    @Excel(name = "会话ID")
    private String sessionId;

    /** 消息角色（user/assistant） */
    @Excel(name = "消息角色")
    private String role;

    /** 消息内容 */
    @Excel(name = "消息内容")
    private String content;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 用户名 */
    @Excel(name = "用户名")
    private String userName;

    /** AI模型 */
    @Excel(name = "AI模型")
    private String model;

    /** 消息状态（0正常 1删除） */
    @Excel(name = "消息状态", readConverterExp = "0=正常,1=删除")
    private String status;

    /** 响应时间（毫秒） */
    @Excel(name = "响应时间")
    private Long responseTime;

    /** Token数量 */
    @Excel(name = "Token数量")
    private Long tokenCount;

    public void setMessageId(String messageId) 
    {
        this.messageId = messageId;
    }

    public String getMessageId() 
    {
        return messageId;
    }
    public void setSessionId(String sessionId) 
    {
        this.sessionId = sessionId;
    }

    public String getSessionId() 
    {
        return sessionId;
    }
    public void setRole(String role) 
    {
        this.role = role;
    }

    public String getRole() 
    {
        return role;
    }
    public void setContent(String content) 
    {
        this.content = content;
    }

    public String getContent() 
    {
        return content;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setUserName(String userName) 
    {
        this.userName = userName;
    }

    public String getUserName() 
    {
        return userName;
    }
    public void setModel(String model) 
    {
        this.model = model;
    }

    public String getModel() 
    {
        return model;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setResponseTime(Long responseTime) 
    {
        this.responseTime = responseTime;
    }

    public Long getResponseTime() 
    {
        return responseTime;
    }
    public void setTokenCount(Long tokenCount) 
    {
        this.tokenCount = tokenCount;
    }

    public Long getTokenCount() 
    {
        return tokenCount;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("messageId", getMessageId())
            .append("sessionId", getSessionId())
            .append("role", getRole())
            .append("content", getContent())
            .append("userId", getUserId())
            .append("userName", getUserName())
            .append("model", getModel())
            .append("status", getStatus())
            .append("responseTime", getResponseTime())
            .append("tokenCount", getTokenCount())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
