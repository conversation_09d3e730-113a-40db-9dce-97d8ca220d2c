import request from '@/utils/request'

// 登录方法
export function login(username, password, code, uuid) {
  const data = {
    username,
    password,
    code,
    uuid
  }
  return request({
    url: '/login',
    headers: {
      isToken: false,
      repeatSubmit: false
    },
    method: 'post',
    data: data
  })
}

// 注册方法
export function register(data) {
  return request({
    url: '/register',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}

// 获取用户详细信息
export function getInfo() {
  return request({
    url: '/getInfo',
    method: 'get'
  })
}

// 退出方法
export function logout() {
  return request({
    url: '/logout',
    method: 'post'
  })
}

// 获取验证码
export function getCodeImg() {
  return request({
    url: '/captchaImage',
    headers: {
      isToken: false
    },
    method: 'get',
    timeout: 20000
  })
}

// 发送短信验证码
export function sendSmsCode(data) {
  return request({
    url: '/sms/send',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}

// 验证手机号和短信验证码
export function verifyPhone(data) {
  return request({
    url: '/forgot-password/verify-phone',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}

// 重置密码
export function resetPassword(data) {
  return request({
    url: '/forgot-password/reset',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}