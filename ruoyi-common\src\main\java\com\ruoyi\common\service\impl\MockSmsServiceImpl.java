package com.ruoyi.common.service.impl;

import com.ruoyi.common.service.SmsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

/**
 * 模拟短信服务实现（用于开发测试环境）
 * 
 * <AUTHOR>
 */
@Service
@ConditionalOnProperty(name = "sms.provider", havingValue = "mock", matchIfMissing = true)
public class MockSmsServiceImpl implements SmsService {
    
    private static final Logger logger = LoggerFactory.getLogger(MockSmsServiceImpl.class);
    
    @Override
    public boolean sendVerificationCode(String phoneNumber, String code) {
        // 模拟发送短信，实际只记录日志
        logger.info("【模拟短信】发送验证码到手机号：{}，验证码：{}", phoneNumber, code);
        System.out.println("=== 短信验证码 ===");
        System.out.println("手机号：" + phoneNumber);
        System.out.println("验证码：" + code);
        System.out.println("================");
        
        return true;
    }
    
    @Override
    public boolean sendNotification(String phoneNumber, String templateCode, String templateParams) {
        // 模拟发送通知短信
        logger.info("【模拟短信】发送通知到手机号：{}，模板：{}，参数：{}", phoneNumber, templateCode, templateParams);
        
        return true;
    }
}
