package com.ruoyi.knowledge.strategy.initialization;

import java.util.HashMap;
import java.util.Map;

/**
 * 初始化策略结果
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
public class InitializationResult {
    
    /** 是否成功 */
    private boolean success;
    
    /** 结果消息 */
    private String message;
    
    /** 处理的文档数量 */
    private int processedDocuments;
    
    /** 创建的知识库ID */
    private Long knowledgeBaseId;
    
    /** 扩展属性 */
    private Map<String, Object> attributes;
    
    public InitializationResult() {
        this.attributes = new HashMap<>();
    }
    
    public InitializationResult(boolean success, String message) {
        this();
        this.success = success;
        this.message = message;
    }
    
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public int getProcessedDocuments() {
        return processedDocuments;
    }
    
    public void setProcessedDocuments(int processedDocuments) {
        this.processedDocuments = processedDocuments;
    }
    
    public Long getKnowledgeBaseId() {
        return knowledgeBaseId;
    }
    
    public void setKnowledgeBaseId(Long knowledgeBaseId) {
        this.knowledgeBaseId = knowledgeBaseId;
    }
    
    public Map<String, Object> getAttributes() {
        return attributes;
    }
    
    public void setAttributes(Map<String, Object> attributes) {
        this.attributes = attributes;
    }
    
    public void setAttribute(String key, Object value) {
        this.attributes.put(key, value);
    }
    
    public Object getAttribute(String key) {
        return this.attributes.get(key);
    }
    
    @Override
    public String toString() {
        return "InitializationResult{" +
                "success=" + success +
                ", message='" + message + '\'' +
                ", processedDocuments=" + processedDocuments +
                ", knowledgeBaseId=" + knowledgeBaseId +
                ", attributes=" + attributes +
                '}';
    }
}
