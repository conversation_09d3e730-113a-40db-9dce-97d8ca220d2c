<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI智能问答 - 若依框架集成演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #409eff, #36cfc9);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header p {
            margin: 10px 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        .content {
            padding: 40px;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 30px 0;
        }
        .feature-card {
            padding: 25px;
            border: 1px solid #e4e7ed;
            border-radius: 8px;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .feature-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .feature-icon {
            font-size: 2.5em;
            margin-bottom: 15px;
        }
        .feature-title {
            font-size: 1.3em;
            font-weight: 600;
            margin-bottom: 10px;
            color: #303133;
        }
        .feature-desc {
            color: #606266;
            line-height: 1.6;
        }
        .demo-section {
            margin: 40px 0;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #409eff;
        }
        .demo-title {
            font-size: 1.5em;
            font-weight: 600;
            margin-bottom: 20px;
            color: #303133;
        }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 6px;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            margin: 15px 0;
        }
        .step-list {
            counter-reset: step-counter;
            list-style: none;
            padding: 0;
        }
        .step-list li {
            counter-increment: step-counter;
            margin: 20px 0;
            padding: 20px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e4e7ed;
            position: relative;
        }
        .step-list li::before {
            content: counter(step-counter);
            position: absolute;
            left: -15px;
            top: 20px;
            background: #409eff;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        .step-title {
            font-weight: 600;
            color: #303133;
            margin-bottom: 10px;
        }
        .highlight {
            background: #fff3cd;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #ffc107;
            margin: 20px 0;
        }
        .success {
            background: #d4edda;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #28a745;
            margin: 20px 0;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #409eff;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            transition: background 0.2s;
            margin: 10px 10px 10px 0;
        }
        .btn:hover {
            background: #337ab7;
        }
        .btn-secondary {
            background: #6c757d;
        }
        .btn-secondary:hover {
            background: #545b62;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 AI智能问答</h1>
            <p>基于若依框架的现代化AI聊天界面</p>
        </div>
        
        <div class="content">
            <div class="success">
                <strong>✅ 集成完成！</strong> AI智能问答模块已成功集成到若依框架中，现在您可以通过菜单系统访问这个功能。
            </div>

            <div class="feature-grid">
                <div class="feature-card">
                    <div class="feature-icon">💬</div>
                    <div class="feature-title">智能对话</div>
                    <div class="feature-desc">支持多种AI模型，提供自然流畅的对话体验，可切换不同的AI助手进行交流。</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">📱</div>
                    <div class="feature-title">响应式设计</div>
                    <div class="feature-desc">完美适配桌面端和移动端，提供一致的用户体验，支持触摸操作和手势交互。</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">🎨</div>
                    <div class="feature-title">现代化UI</div>
                    <div class="feature-desc">仿腾讯元宝的聊天界面设计，美观大方，支持消息气泡、打字效果等交互动画。</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">🔒</div>
                    <div class="feature-title">权限控制</div>
                    <div class="feature-desc">完全集成若依权限系统，支持角色权限控制，确保系统安全性。</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">💾</div>
                    <div class="feature-title">会话管理</div>
                    <div class="feature-desc">支持多会话管理，历史记录保存，消息导出等实用功能。</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">⚡</div>
                    <div class="feature-title">实时交互</div>
                    <div class="feature-desc">支持实时消息发送，打字指示器，消息状态显示等现代聊天功能。</div>
                </div>
            </div>

            <div class="demo-section">
                <div class="demo-title">🚀 快速开始</div>
                <ol class="step-list">
                    <li>
                        <div class="step-title">添加菜单</div>
                        <p>在若依后台管理系统中添加"智能问答"菜单：</p>
                        <div class="code-block">菜单名称：智能问答
路由地址：/ai/chat
组件路径：ai/chat/index
权限字符：ai:chat:view</div>
                    </li>
                    
                    <li>
                        <div class="step-title">配置权限</div>
                        <p>为相应角色分配AI聊天相关权限：</p>
                        <div class="code-block">ai:chat:view    - 查看智能问答页面
ai:chat:send    - 发送消息
ai:chat:history - 查看历史记录
ai:chat:session - 管理会话</div>
                    </li>
                    
                    <li>
                        <div class="step-title">后端接口</div>
                        <p>实现相应的后端API接口（当前使用模拟数据）：</p>
                        <div class="code-block">POST /ai/chat/send          # 发送消息
GET  /ai/chat/history       # 获取聊天历史
POST /ai/chat/session       # 创建会话
GET  /ai/chat/sessions      # 获取会话列表</div>
                    </li>
                    
                    <li>
                        <div class="step-title">访问页面</div>
                        <p>通过若依系统菜单访问AI智能问答功能，开始与AI助手对话。</p>
                    </li>
                </ol>
            </div>

            <div class="highlight">
                <strong>💡 提示：</strong> 当前版本在开发环境中使用模拟数据进行演示。在生产环境中，请确保后端API接口已正确实现并配置相应的AI服务。
            </div>

            <div class="demo-section">
                <div class="demo-title">📁 文件结构</div>
                <div class="code-block">src/views/ai/
├── chat/
│   └── index.vue          # 主聊天界面
├── README.md              # 集成文档
└── route-config.js        # 路由配置示例

src/api/ai/
├── chat.js                # API接口
└── mock.js                # 模拟数据</div>
            </div>

            <div class="demo-section">
                <div class="demo-title">🛠️ 技术栈</div>
                <ul>
                    <li><strong>前端框架：</strong> Vue 3 + Composition API</li>
                    <li><strong>UI组件：</strong> Element Plus</li>
                    <li><strong>构建工具：</strong> Vite</li>
                    <li><strong>样式预处理：</strong> SCSS</li>
                    <li><strong>状态管理：</strong> Pinia (若依集成)</li>
                    <li><strong>路由管理：</strong> Vue Router 4</li>
                </ul>
            </div>

            <div style="text-align: center; margin-top: 40px;">
                <a href="#" class="btn" onclick="alert('请通过若依系统菜单访问AI智能问答功能')">
                    🚀 开始使用
                </a>
                <a href="src/views/ai/README.md" class="btn btn-secondary">
                    📖 查看文档
                </a>
            </div>
        </div>
    </div>

    <script>
        // 简单的交互效果
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.feature-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.borderColor = '#409eff';
                });
                card.addEventListener('mouseleave', function() {
                    this.style.borderColor = '#e4e7ed';
                });
            });
        });
    </script>
</body>
</html>
