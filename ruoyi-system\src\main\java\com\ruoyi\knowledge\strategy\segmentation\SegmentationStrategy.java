package com.ruoyi.knowledge.strategy.segmentation;

import com.ruoyi.knowledge.strategy.KnowledgeStrategy;
import com.ruoyi.knowledge.enums.StrategyType;

/**
 * 段落切分策略接口
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
public interface SegmentationStrategy extends KnowledgeStrategy<SegmentationContext, SegmentationResult> {
    
    @Override
    default StrategyType getStrategyType() {
        return StrategyType.SEGMENTATION;
    }
    
    @Override
    default int getExecutionPriority() {
        return 20; // 段落切分策略优先级较高
    }
}
