package com.ruoyi.knowledge.strategy.initialization;

import com.ruoyi.knowledge.strategy.KnowledgeStrategy;
import com.ruoyi.knowledge.enums.StrategyType;

/**
 * 初始化策略接口
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
public interface InitializationStrategy extends KnowledgeStrategy<InitializationContext, InitializationResult> {
    
    @Override
    default StrategyType getStrategyType() {
        return StrategyType.INITIALIZATION;
    }
    
    @Override
    default int getExecutionPriority() {
        return 10; // 初始化策略优先级最高
    }
    
    @Override
    default boolean supportParallelExecution() {
        return false; // 初始化策略不支持并行执行
    }
}
