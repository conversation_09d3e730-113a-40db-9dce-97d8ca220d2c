<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.knowledge.mapper.KnowledgeFolderMapper">
    
    <resultMap type="KnowledgeFolder" id="KnowledgeFolderResult">
        <result property="id"                    column="id"                    />
        <result property="name"                  column="name"                  />
        <result property="description"           column="description"           />
        <result property="parentId"              column="parent_id"             />
        <result property="knowledgeBaseId"       column="knowledge_base_id"     />
        <result property="path"                  column="path"                  />
        <result property="level"                 column="level"                 />
        <result property="orderNum"              column="order_num"             />
        <result property="status"                column="status"                />
        <result property="isPublic"              column="is_public"             />
        <result property="documentCount"         column="document_count"        />
        <result property="subFolderCount"        column="sub_folder_count"      />
        <result property="creatorId"             column="creator_id"            />
        <result property="creatorName"           column="creator_name"          />
        <result property="createBy"              column="create_by"             />
        <result property="createTime"            column="create_time"           />
        <result property="updateBy"              column="update_by"             />
        <result property="updateTime"            column="update_time"           />
        <result property="remark"                column="remark"                />
    </resultMap>

    <sql id="selectKnowledgeFolderVo">
        select id, name, description, parent_id, knowledge_base_id, path, level, order_num, status, is_public, document_count, sub_folder_count, creator_id, creator_name, create_by, create_time, update_by, update_time, remark from knowledge_folder
    </sql>

    <select id="selectKnowledgeFolderList" parameterType="KnowledgeFolder" resultMap="KnowledgeFolderResult">
        <include refid="selectKnowledgeFolderVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="knowledgeBaseId != null "> and knowledge_base_id = #{knowledgeBaseId}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="isPublic != null  and isPublic != ''"> and is_public = #{isPublic}</if>
            <if test="creatorId != null "> and creator_id = #{creatorId}</if>
        </where>
        order by parent_id, order_num
    </select>
    
    <select id="selectKnowledgeFolderById" parameterType="Long" resultMap="KnowledgeFolderResult">
        <include refid="selectKnowledgeFolderVo"/>
        where id = #{id}
    </select>

    <select id="selectKnowledgeFolderTree" parameterType="Long" resultMap="KnowledgeFolderResult">
        <include refid="selectKnowledgeFolderVo"/>
        <where>
            status = '0'
            <if test="knowledgeBaseId != null"> and knowledge_base_id = #{knowledgeBaseId}</if>
        </where>
        order by parent_id, order_num
    </select>

    <select id="selectKnowledgeFolderByParentId" parameterType="Long" resultMap="KnowledgeFolderResult">
        <include refid="selectKnowledgeFolderVo"/>
        where parent_id = #{parentId} and status = '0'
        order by order_num
    </select>

    <select id="selectRootFoldersByKnowledgeBaseId" parameterType="Long" resultMap="KnowledgeFolderResult">
        <include refid="selectKnowledgeFolderVo"/>
        where parent_id = 0 and status = '0'
        <if test="knowledgeBaseId != null"> and knowledge_base_id = #{knowledgeBaseId}</if>
        order by order_num
    </select>

    <select id="checkFolderNameUnique" parameterType="KnowledgeFolder" resultType="int">
        select count(1) from knowledge_folder 
        where name = #{name} and parent_id = #{parentId}
        <if test="id != null and id != 0"> and id != #{id}</if>
    </select>

    <select id="countSubFoldersByParentId" parameterType="Long" resultType="Long">
        select count(*) from knowledge_folder where parent_id = #{parentId} and status = '0'
    </select>
        
    <insert id="insertKnowledgeFolder" parameterType="KnowledgeFolder" useGeneratedKeys="true" keyProperty="id">
        insert into knowledge_folder
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="description != null">description,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="knowledgeBaseId != null">knowledge_base_id,</if>
            <if test="path != null and path != ''">path,</if>
            <if test="level != null">level,</if>
            <if test="orderNum != null">order_num,</if>
            <if test="status != null">status,</if>
            <if test="isPublic != null">is_public,</if>
            <if test="documentCount != null">document_count,</if>
            <if test="subFolderCount != null">sub_folder_count,</if>
            <if test="creatorId != null">creator_id,</if>
            <if test="creatorName != null">creator_name,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="description != null">#{description},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="knowledgeBaseId != null">#{knowledgeBaseId},</if>
            <if test="path != null and path != ''">#{path},</if>
            <if test="level != null">#{level},</if>
            <if test="orderNum != null">#{orderNum},</if>
            <if test="status != null">#{status},</if>
            <if test="isPublic != null">#{isPublic},</if>
            <if test="documentCount != null">#{documentCount},</if>
            <if test="subFolderCount != null">#{subFolderCount},</if>
            <if test="creatorId != null">#{creatorId},</if>
            <if test="creatorName != null">#{creatorName},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateKnowledgeFolder" parameterType="KnowledgeFolder">
        update knowledge_folder
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="description != null">description = #{description},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="knowledgeBaseId != null">knowledge_base_id = #{knowledgeBaseId},</if>
            <if test="path != null and path != ''">path = #{path},</if>
            <if test="level != null">level = #{level},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="status != null">status = #{status},</if>
            <if test="isPublic != null">is_public = #{isPublic},</if>
            <if test="documentCount != null">document_count = #{documentCount},</if>
            <if test="subFolderCount != null">sub_folder_count = #{subFolderCount},</if>
            <if test="creatorId != null">creator_id = #{creatorId},</if>
            <if test="creatorName != null">creator_name = #{creatorName},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteKnowledgeFolderById" parameterType="Long">
        delete from knowledge_folder where id = #{id}
    </delete>

    <delete id="deleteKnowledgeFolderByIds" parameterType="String">
        delete from knowledge_folder where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateDocumentCount">
        update knowledge_folder set document_count = #{documentCount} where id = #{folderId}
    </update>

    <update id="updateSubFolderCount">
        update knowledge_folder set sub_folder_count = #{subFolderCount} where id = #{parentId}
    </update>
</mapper>
