package com.ruoyi.knowledge.service;

import java.util.List;
import com.ruoyi.knowledge.domain.KnowledgeBasePermission;

/**
 * 知识库权限Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-22
 */
public interface IKnowledgeBasePermissionService 
{
    /**
     * 查询知识库权限
     * 
     * @param id 知识库权限主键
     * @return 知识库权限
     */
    public KnowledgeBasePermission selectKnowledgeBasePermissionById(Long id);

    /**
     * 查询知识库权限列表
     * 
     * @param knowledgeBasePermission 知识库权限
     * @return 知识库权限集合
     */
    public List<KnowledgeBasePermission> selectKnowledgeBasePermissionList(KnowledgeBasePermission knowledgeBasePermission);

    /**
     * 根据用户ID查询知识库权限列表
     * 
     * @param userId 用户ID
     * @return 知识库权限集合
     */
    public List<KnowledgeBasePermission> selectKnowledgeBasePermissionByUserId(Long userId);

    /**
     * 根据知识库ID查询权限列表
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 知识库权限集合
     */
    public List<KnowledgeBasePermission> selectKnowledgeBasePermissionByKnowledgeBaseId(Long knowledgeBaseId);

    /**
     * 检查用户对知识库的权限
     * 
     * @param knowledgeBaseId 知识库ID
     * @param userId 用户ID
     * @return 知识库权限
     */
    public KnowledgeBasePermission selectUserKnowledgeBasePermission(Long knowledgeBaseId, Long userId);

    /**
     * 检查用户是否有知识库的指定权限
     * 
     * @param knowledgeBaseId 知识库ID
     * @param userId 用户ID
     * @param requiredPermission 需要的权限类型
     * @return 是否有权限
     */
    public boolean hasKnowledgeBasePermission(Long knowledgeBaseId, Long userId, String requiredPermission);

    /**
     * 新增知识库权限
     * 
     * @param knowledgeBasePermission 知识库权限
     * @return 结果
     */
    public int insertKnowledgeBasePermission(KnowledgeBasePermission knowledgeBasePermission);

    /**
     * 修改知识库权限
     * 
     * @param knowledgeBasePermission 知识库权限
     * @return 结果
     */
    public int updateKnowledgeBasePermission(KnowledgeBasePermission knowledgeBasePermission);

    /**
     * 批量删除知识库权限
     * 
     * @param ids 需要删除的知识库权限主键集合
     * @return 结果
     */
    public int deleteKnowledgeBasePermissionByIds(Long[] ids);

    /**
     * 删除知识库权限信息
     * 
     * @param id 知识库权限主键
     * @return 结果
     */
    public int deleteKnowledgeBasePermissionById(Long id);

    /**
     * 根据知识库ID删除权限
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 结果
     */
    public int deleteKnowledgeBasePermissionByKnowledgeBaseId(Long knowledgeBaseId);

    /**
     * 根据用户ID删除权限
     * 
     * @param userId 用户ID
     * @return 结果
     */
    public int deleteKnowledgeBasePermissionByUserId(Long userId);

    /**
     * 批量授权
     * 
     * @param knowledgeBaseId 知识库ID
     * @param userIds 用户ID列表
     * @param permissionType 权限类型
     * @param grantedBy 授权人ID
     * @param grantedByName 授权人名称
     * @return 结果
     */
    public int batchGrantPermissions(Long knowledgeBaseId, Long[] userIds, String permissionType, Long grantedBy, String grantedByName);

    /**
     * 为知识库创建者自动授权管理员权限
     * 
     * @param knowledgeBaseId 知识库ID
     * @param creatorId 创建者ID
     * @param creatorName 创建者名称
     * @return 结果
     */
    public int grantCreatorAdminPermission(Long knowledgeBaseId, Long creatorId, String creatorName);
}
