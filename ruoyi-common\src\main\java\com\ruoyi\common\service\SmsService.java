package com.ruoyi.common.service;

/**
 * 短信服务接口
 * 
 * <AUTHOR>
 */
public interface SmsService {
    
    /**
     * 发送验证码短信
     * 
     * @param phoneNumber 手机号码
     * @param code 验证码
     * @return 是否发送成功
     */
    boolean sendVerificationCode(String phoneNumber, String code);
    
    /**
     * 发送通知短信
     * 
     * @param phoneNumber 手机号码
     * @param templateCode 模板代码
     * @param templateParams 模板参数
     * @return 是否发送成功
     */
    boolean sendNotification(String phoneNumber, String templateCode, String templateParams);
}
