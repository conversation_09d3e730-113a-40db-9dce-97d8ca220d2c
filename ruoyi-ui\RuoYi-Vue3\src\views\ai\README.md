# AI 智能问答模块集成指南

## 概述

本模块提供了一个类似腾讯元宝的 AI 智能问答界面，集成到若依框架中。

## 文件结构

```
src/views/ai/
├── chat/
│   └── index.vue          # 主聊天界面
└── README.md              # 本文档

src/api/ai/
└── chat.js                # AI聊天相关API接口
```

## 功能特性

- 🤖 智能对话：支持多种 AI 模型切换
- 💬 会话管理：创建、切换、删除对话会话
- 📱 响应式设计：完美适配桌面端和移动端
- 🎨 现代化 UI：仿腾讯元宝的聊天界面
- ⚡ 实时交互：打字效果、消息动画
- 📋 实用功能：消息复制、重新生成、导出记录
- 🔒 权限控制：集成若依权限系统

## 菜单配置

### 1. 在若依后台管理系统中添加菜单

根据您提供的菜单添加截图，请按以下配置添加菜单：

**菜单信息：**

- 菜单名称：智能问答
- 上级菜单：主类目（或根据需要选择）
- 菜单类型：菜单
- 菜单图标：search（或其他合适图标）
- 显示排序：根据需要设置
- 是否外链：否
- 路由地址：/ai/chat
- 组件路径：ai/chat/index
- 权限字符：ai:chat:view
- 是否缓存：缓存
- 显示状态：显示
- 菜单状态：正常

### 2. 权限配置

确保用户角色具有以下权限：

- `ai:chat:view` - 查看智能问答页面
- `ai:chat:send` - 发送消息
- `ai:chat:history` - 查看历史记录
- `ai:chat:session` - 管理会话

## 后端接口说明

需要在后端实现以下 API 接口：

### 聊天相关接口

```
POST /ai/chat/send          # 发送消息
GET  /ai/chat/history       # 获取聊天历史
POST /ai/chat/session       # 创建会话
GET  /ai/chat/sessions      # 获取会话列表
DELETE /ai/chat/session/:id # 删除会话
DELETE /ai/chat/clear/:id   # 清空聊天记录
GET  /ai/models             # 获取AI模型列表
POST /ai/chat/stream        # 流式聊天（可选）
```

### 请求/响应格式示例

**发送消息：**

```json
// 请求
{
  "sessionId": "session_123",
  "message": "你好，请介绍一下自己",
  "model": "qwen-plus"
}

// 响应
{
  "code": 200,
  "msg": "success",
  "data": {
    "content": "您好！我是AI助手...",
    "messageId": "msg_456"
  }
}
```

**获取会话列表：**

```json
// 响应
{
  "code": 200,
  "msg": "success",
  "data": [
    {
      "id": "session_123",
      "title": "关于AI的讨论",
      "createTime": "2024-01-01 10:00:00",
      "updateTime": "2024-01-01 10:30:00"
    }
  ]
}
```

## 部署步骤

### 1. 前端部署

1. 确保文件已正确放置在对应目录
2. 重新构建前端项目：`npm run build:prod`
3. 部署到 Web 服务器

### 2. 后端配置

1. 实现上述 API 接口
2. 配置 AI 模型服务（如 OpenAI、通义千问等）
3. 添加相应的权限控制
4. 在数据库中添加菜单记录

### 3. 数据库配置

在`sys_menu`表中插入菜单记录：

```sql
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('智能问答', 0, 4, 'ai', NULL, 1, 0, 'M', '0', '0', NULL, 'search', 'admin', sysdate(), '', NULL, '智能问答菜单');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('智能问答', (SELECT menu_id FROM sys_menu WHERE menu_name = '智能问答' AND parent_id = 0), 1, 'chat', 'ai/chat/index', 1, 0, 'C', '0', '0', 'ai:chat:view', 'message', 'admin', sysdate(), '', NULL, '智能问答页面');
```

## 自定义配置

### 1. 修改 AI 模型列表

在 `src/views/ai/chat/index.vue` 中修改 `aiModels` 数组：

```javascript
const aiModels = ref([{ label: "qwen-plus", value: "qwen-plus" }]);
```

### 2. 修改样式主题

可以通过修改 CSS 变量来自定义主题色彩：

```scss
:root {
  --ai-primary-color: #409eff;
  --ai-bg-color: #f5f5f5;
  --ai-message-bg: #ffffff;
}
```

### 3. 添加自定义功能

可以在现有基础上扩展更多功能：

- 语音输入/输出
- 文件上传
- 图片识别
- 代码高亮
- Markdown 渲染

## 注意事项

1. **权限控制**：确保正确配置用户权限，避免未授权访问
2. **API 安全**：后端接口需要进行身份验证和权限校验
3. **性能优化**：对于大量历史消息，建议实现分页加载
4. **错误处理**：完善错误处理机制，提供友好的错误提示
5. **数据备份**：重要的聊天记录建议定期备份

## 技术支持

如有问题，请检查：

1. 浏览器控制台是否有错误信息
2. 网络请求是否正常
3. 后端 API 是否正确实现
4. 权限配置是否正确

## 更新日志

- v1.0.0: 初始版本，基础聊天功能
- 支持多会话管理
- 响应式设计
- 消息操作功能
