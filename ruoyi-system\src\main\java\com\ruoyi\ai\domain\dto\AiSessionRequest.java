package com.ruoyi.ai.domain.dto;

import javax.validation.constraints.Size;

/**
 * AI会话创建请求DTO
 * 
 * <AUTHOR>
 * @date 2024-07-09
 */
public class AiSessionRequest
{
    /** 会话标题 */
    @Size(max = 100, message = "会话标题不能超过100个字符")
    private String title = "新对话";

    /** AI模型 */
    private String model = "qwen-plus";

    /** 备注 */
    @Size(max = 500, message = "备注不能超过500个字符")
    private String remark;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String toString() {
        return "AiSessionRequest{" +
                "title='" + title + '\'' +
                ", model='" + model + '\'' +
                ", remark='" + remark + '\'' +
                '}';
    }
}
