<template>
  <div class="ai-chat-container">
    <!-- 移动端遮罩层 -->
    <div
      v-if="mobileSidebarVisible"
      class="mobile-overlay"
      @click="mobileSidebarVisible = false"
    ></div>

    <!-- 侧边栏 - 会话列表 -->
    <div class="chat-sidebar" :class="{ 'mobile-show': mobileSidebarVisible }">
      <div class="sidebar-header">
        <el-button
          type="primary"
          @click="createNewSession"
          :icon="Plus"
          class="new-chat-btn"
        >
          新建对话
        </el-button>
      </div>
      
      <div class="session-list">
        <div 
          v-for="session in sessions" 
          :key="session.id"
          class="session-item"
          :class="{ 'active': currentSessionId === session.id }"
          @click="switchSession(session.id)"
        >
          <div class="session-title">{{ session.title || '新对话' }}</div>
          <div class="session-time">{{ formatTime(session.updateTime) }}</div>
          <el-dropdown trigger="click" @command="handleSessionCommand">
            <el-button text :icon="MoreFilled" class="session-menu" />
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item :command="{action: 'delete', id: session.id}">
                  删除对话
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>

    <!-- 主聊天区域 -->
    <div class="chat-main">
      <!-- 聊天头部 -->
      <div class="chat-header">
        <div class="chat-title">
          <el-button
            class="mobile-menu-btn"
            text
            @click="toggleMobileSidebar"
            :icon="Menu"
          />
          <el-icon><ChatDotRound /></el-icon>
          <span>智能问答助手</span>
        </div>
        <div class="chat-actions">
          <span class="model-display">模型: qwen-plus</span>
          <el-select
            v-model="selectedKnowledgeBase"
            placeholder="选择知识库"
            clearable
            size="small"
            style="width: 150px; margin-right: 8px;"
            @change="handleKnowledgeBaseChange"
          >
            <el-option label="不使用知识库" :value="null" />
            <el-option
              v-for="kb in knowledgeBaseList"
              :key="kb.id"
              :label="kb.name"
              :value="kb.id"
            />
          </el-select>
          <el-button @click="showHistoryDialog" :icon="Clock" text>查看历史</el-button>
          <el-button @click="exportChat" :icon="Download" text>导出记录</el-button>
          <el-button @click="clearCurrentChat" :icon="Delete" text>清空对话</el-button>
        </div>
      </div>

      <!-- 消息列表 -->
      <div class="message-list" ref="messageListRef">
        <div v-if="messages.length === 0" class="empty-state">
          <el-icon size="64" color="#909399"><ChatDotRound /></el-icon>
          <p>开始与AI助手对话吧！</p>
        </div>
        
        <transition-group name="message" tag="div">
          <div
            v-for="message in messages"
            :key="message.id"
            class="message-item"
            :class="{ 'user-message': message.role === 'user', 'ai-message': message.role === 'assistant' }"
          >
            <div class="message-avatar">
              <el-avatar v-if="message.role === 'user'" :icon="UserFilled" />
              <el-avatar v-else>
                <el-icon><ChatDotRound /></el-icon>
              </el-avatar>
            </div>
            <div class="message-content">
              <div class="message-text" v-html="formatMessage(message.content)"></div>
              <div class="message-actions">
                <el-button
                  text
                  size="small"
                  @click="copyMessage(message.content)"
                  title="复制"
                >
                  <el-icon><DocumentCopy /></el-icon>
                </el-button>
                <el-button
                  v-if="message.role === 'assistant'"
                  text
                  size="small"
                  @click="regenerateMessage(message)"
                  title="重新生成"
                >
                  <el-icon><RefreshRight /></el-icon>
                </el-button>
              </div>
              <div class="message-time">{{ formatTime(message.createTime) }}</div>
            </div>
          </div>
        </transition-group>

        <!-- 加载中状态 -->
        <div v-if="isLoading" class="message-item ai-message">
          <div class="message-avatar">
            <el-avatar>
              <el-icon><ChatDotRound /></el-icon>
            </el-avatar>
          </div>
          <div class="message-content">
            <div class="typing-indicator">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="input-area">
        <div class="input-container">
          <el-input
            v-model="inputMessage"
            type="textarea"
            :rows="1"
            :autosize="{ minRows: 1, maxRows: 4 }"
            placeholder="输入您的问题..."
            @keydown.enter.exact.prevent="sendMessage"
            @keydown.enter.shift.exact="handleShiftEnter"
            :disabled="isLoading"
            class="message-input"
          />
          <el-button 
            type="primary" 
            @click="sendMessage"
            :loading="isLoading"
            :disabled="!inputMessage.trim()"
            :icon="Promotion"
            class="send-btn"
          >
            发送
          </el-button>
        </div>
        <div class="input-tips">
          <span>按 Enter 发送，Shift + Enter 换行</span>
        </div>
      </div>
    </div>
  </div>

  <!-- 历史记录对话框 -->
  <el-dialog
    v-model="historyDialogVisible"
    title="聊天历史记录"
    width="70%"
    :before-close="handleHistoryDialogClose"
  >
    <div class="history-content">
      <div class="history-filters">
        <el-date-picker
          v-model="historyDateRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          @change="filterHistoryByDate"
          style="margin-right: 10px;"
        />
        <el-button @click="refreshHistory" :icon="RefreshRight">刷新</el-button>
      </div>

      <div class="history-list" v-loading="historyLoading">
        <div
          v-for="message in filteredHistory"
          :key="message.id"
          class="history-message"
          :class="{ 'user-message': message.role === 'user', 'assistant-message': message.role === 'assistant' }"
        >
          <div class="message-header">
            <el-icon v-if="message.role === 'user'"><UserFilled /></el-icon>
            <el-icon v-else><ChatDotRound /></el-icon>
            <span class="message-role">{{ message.role === 'user' ? '用户' : 'AI助手' }}</span>
            <span class="message-time">{{ formatTime(message.createTime) }}</span>
          </div>
          <div class="message-content">{{ message.content }}</div>
        </div>

        <div v-if="filteredHistory.length === 0" class="empty-history">
          <el-icon size="48" color="#909399"><ChatDotRound /></el-icon>
          <p>暂无历史记录</p>
        </div>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="exportHistoryToFile" :icon="Download">导出历史</el-button>
        <el-button @click="historyDialogVisible = false">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup name="AiChat">
import { ref, reactive, onMounted, nextTick, computed } from 'vue'
import {
  Plus, MoreFilled, ChatDotRound,
  UserFilled, Delete, Promotion, DocumentCopy, RefreshRight, Download, Menu, Clock
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  sendMessage as apiSendMessage,
  getChatHistory,
  createChatSession,
  getChatSessions,
  deleteChatSession,
  clearChatHistory,
  getAiModels,

} from '@/api/ai/chat'
import { listKnowledgeBase } from '@/api/knowledge/build'

// 响应式数据
const mobileSidebarVisible = ref(false)
const currentSessionId = ref(null)
const sessions = ref([])
const messages = ref([])
const inputMessage = ref('')
const isLoading = ref(false)
const messageListRef = ref(null)
const selectedModel = ref('qwen-plus')

// AI模型列表
const aiModels = ref([
  { label: 'qwen-plus', value: 'qwen-plus' }
])

// 知识库相关
const knowledgeBaseList = ref([])
const selectedKnowledgeBase = ref(null)

// 历史记录相关
const historyDialogVisible = ref(false)
const historyLoading = ref(false)
const historyMessages = ref([])
const filteredHistory = ref([])
const historyDateRange = ref([])

// 方法
const toggleMobileSidebar = () => {
  mobileSidebarVisible.value = !mobileSidebarVisible.value
}

const createNewSession = async () => {
  try {
    const response = await createChatSession({
      title: '新对话',
      model: selectedModel.value
    })
    // 适配若依框架的响应格式
    const newSession = response.data || response
    sessions.value.unshift(newSession)
    currentSessionId.value = newSession.sessionId || newSession.id
    messages.value = []
  } catch (error) {
    console.error('创建会话失败:', error)
    ElMessage.error('创建会话失败: ' + (error.message || '未知错误'))
  }
}

const switchSession = async (sessionId) => {
  currentSessionId.value = sessionId
  await loadChatHistory(sessionId)
}

const loadChatHistory = async (sessionId) => {
  if (!sessionId) {
    console.warn('会话ID为空，跳过加载聊天记录')
    return
  }

  try {
    const response = await getChatHistory({ sessionId })
    // 适配若依框架的响应格式 - 通常数据在response.rows中
    const historyData = response.rows || response.data || []

    // 转换后端数据格式为前端格式
    messages.value = historyData.map(msg => ({
      id: msg.messageId || msg.id,
      role: msg.role,
      content: msg.content,
      createTime: new Date(msg.createTime)
    }))

    scrollToBottom()
  } catch (error) {
    console.error('加载聊天记录失败:', error)
    ElMessage.error('加载聊天记录失败: ' + (error.message || '未知错误'))
  }
}

const sendMessage = async () => {
  if (!inputMessage.value.trim() || isLoading.value) return

  // 确保有会话ID，如果没有则创建
  if (!currentSessionId.value) {
    try {
      await createNewSession()
    } catch (error) {
      console.error('创建会话失败:', error)
      ElMessage.error('创建会话失败，请重试')
      return
    }
  }

  // 如果仍然没有会话ID，使用临时ID
  if (!currentSessionId.value) {
    currentSessionId.value = 'temp-session-' + Date.now()
    console.warn('使用临时会话ID:', currentSessionId.value)
  }

  const userMessage = {
    id: Date.now(),
    role: 'user',
    content: inputMessage.value,
    createTime: new Date()
  }

  messages.value.push(userMessage)
  const messageContent = inputMessage.value
  inputMessage.value = ''
  isLoading.value = true

  // 添加临时的"AI正在思考"消息
  const thinkingMessage = {
    id: 'thinking-' + Date.now(),
    role: 'assistant',
    content: 'AI正在思考中，请稍候...',
    createTime: new Date(),
    isThinking: true
  }
  messages.value.push(thinkingMessage)

  scrollToBottom()

  // 使用同步方式发送消息
  await sendMessageSync(messageContent)
}

// 同步消息发送
const sendMessageSync = async (messageContent) => {
  try {
    console.log('发送消息参数:', {
      sessionId: currentSessionId.value,
      message: messageContent,
      model: selectedModel.value,
      knowledgeBaseId: selectedKnowledgeBase.value
    })

    const response = await apiSendMessage({
      sessionId: currentSessionId.value,
      message: messageContent,
      model: selectedModel.value,
      knowledgeBaseId: selectedKnowledgeBase.value
    })

    // 移除"AI正在思考"消息
    const thinkingIndex = messages.value.findIndex(msg => msg.isThinking)
    if (thinkingIndex !== -1) {
      messages.value.splice(thinkingIndex, 1)
    }

    // 适配若依框架的响应格式
    const responseData = response.data || response

    const aiMessage = {
      id: responseData.messageId || Date.now() + 1,
      role: 'assistant',
      content: responseData.content,
      createTime: new Date()
    }

    messages.value.push(aiMessage)

    // 更新会话标题（如果是第一条消息）
    if (messages.value.length === 2) {
      updateSessionTitle(currentSessionId.value, messageContent)
    }

    scrollToBottom()
  } catch (error) {
    console.error('发送消息失败:', error)

    // 移除"AI正在思考"消息
    const thinkingIndex = messages.value.findIndex(msg => msg.isThinking)
    if (thinkingIndex !== -1) {
      messages.value.splice(thinkingIndex, 1)
    }

    let errorMessage = '发送消息失败'

    if (error.message && error.message.includes('timeout')) {
      errorMessage = 'AI响应超时，请稍后重试'
    } else if (error.message && error.message.includes('网络')) {
      errorMessage = '网络连接异常，请检查网络后重试'
    } else if (error.response && error.response.status === 500) {
      errorMessage = 'AI服务暂时不可用，请稍后重试'
    }

    ElMessage.error(errorMessage)
    showRetryOption(messageContent)
  } finally {
    isLoading.value = false
  }
}





// 更新会话标题
const updateSessionTitle = (sessionId, firstMessage) => {
  const session = sessions.value.find(s => s.id === sessionId)
  if (session) {
    session.title = firstMessage.length > 20 ? firstMessage.substring(0, 20) + '...' : firstMessage
  }
}

// 显示重试选项
const showRetryOption = (messageContent) => {
  ElMessageBox.confirm('消息发送失败，是否重试？', '提示', {
    confirmButtonText: '重试',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    inputMessage.value = messageContent
  }).catch(() => {
    // 用户取消重试
  })
}

// 复制消息内容
const copyMessage = (content) => {
  navigator.clipboard.writeText(content).then(() => {
    ElMessage.success('已复制到剪贴板')
  }).catch(() => {
    ElMessage.error('复制失败')
  })
}

// 重新生成消息
const regenerateMessage = async (message) => {
  if (isLoading.value) return

  // 找到该消息的索引
  const messageIndex = messages.value.findIndex(m => m.id === message.id)
  if (messageIndex === -1) return

  // 找到对应的用户消息
  let userMessage = null
  for (let i = messageIndex - 1; i >= 0; i--) {
    if (messages.value[i].role === 'user') {
      userMessage = messages.value[i]
      break
    }
  }

  if (!userMessage) return

  // 删除当前AI消息
  messages.value.splice(messageIndex, 1)
  isLoading.value = true

  try {
    const response = await apiSendMessage({
      sessionId: currentSessionId.value,
      message: userMessage.content,
      model: selectedModel.value,
      knowledgeBaseId: selectedKnowledgeBase.value
    })

    const newAiMessage = {
      id: Date.now(),
      role: 'assistant',
      content: response.data.content,
      createTime: new Date()
    }

    messages.value.splice(messageIndex, 0, newAiMessage)
    scrollToBottom()
  } catch (error) {
    ElMessage.error('重新生成失败')
    // 恢复原消息
    messages.value.splice(messageIndex, 0, message)
  } finally {
    isLoading.value = false
  }
}

// 导出聊天记录
const exportChat = () => {
  if (messages.value.length === 0) {
    ElMessage.warning('暂无聊天记录')
    return
  }

  const chatContent = messages.value.map(msg => {
    const role = msg.role === 'user' ? '用户' : 'AI助手'
    const time = formatTime(msg.createTime)
    return `[${time}] ${role}: ${msg.content}`
  }).join('\n\n')

  const blob = new Blob([chatContent], { type: 'text/plain;charset=utf-8' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `聊天记录_${new Date().toLocaleDateString()}.txt`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

const handleShiftEnter = (event) => {
  // Shift + Enter 换行，不发送消息
  return true
}

const clearCurrentChat = async () => {
  if (!currentSessionId.value) return
  
  try {
    await ElMessageBox.confirm('确定要清空当前对话吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await clearChatHistory(currentSessionId.value)
    messages.value = []
    ElMessage.success('对话已清空')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('清空对话失败')
    }
  }
}

const handleSessionCommand = async (command) => {
  if (command.action === 'delete') {
    try {
      await ElMessageBox.confirm('确定要删除这个对话吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      
      await deleteChatSession(command.id)
      sessions.value = sessions.value.filter(s => s.id !== command.id)
      
      if (currentSessionId.value === command.id) {
        currentSessionId.value = null
        messages.value = []
      }
      
      ElMessage.success('对话已删除')
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('删除对话失败')
      }
    }
  }
}

const formatMessage = (content) => {
  // 简单的markdown渲染
  return content
    .replace(/\n/g, '<br>')
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/`(.*?)`/g, '<code>$1</code>')
}

const formatTime = (time) => {
  if (!time) return ''
  const date = new Date(time)
  const now = new Date()
  const diff = now - date

  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  return date.toLocaleDateString()
}

// 历史记录相关方法
const showHistoryDialog = async () => {
  if (!currentSessionId.value) {
    ElMessage.warning('请先选择一个会话')
    return
  }

  historyDialogVisible.value = true
  await loadHistoryMessages()
}

const loadHistoryMessages = async () => {
  if (!currentSessionId.value) return

  historyLoading.value = true
  try {
    const response = await getChatHistory({ sessionId: currentSessionId.value })
    const historyData = response.rows || response.data || []

    historyMessages.value = historyData.map(msg => ({
      id: msg.messageId || msg.id,
      role: msg.role,
      content: msg.content,
      createTime: new Date(msg.createTime)
    }))

    filteredHistory.value = [...historyMessages.value]
  } catch (error) {
    console.error('加载历史记录失败:', error)
    ElMessage.error('加载历史记录失败')
  } finally {
    historyLoading.value = false
  }
}

const filterHistoryByDate = () => {
  if (!historyDateRange.value || historyDateRange.value.length !== 2) {
    filteredHistory.value = [...historyMessages.value]
    return
  }

  const [startDate, endDate] = historyDateRange.value
  const start = new Date(startDate)
  const end = new Date(endDate)

  filteredHistory.value = historyMessages.value.filter(msg => {
    const msgDate = new Date(msg.createTime)
    return msgDate >= start && msgDate <= end
  })
}

const refreshHistory = async () => {
  await loadHistoryMessages()
  ElMessage.success('历史记录已刷新')
}

const exportHistoryToFile = () => {
  if (filteredHistory.value.length === 0) {
    ElMessage.warning('没有可导出的历史记录')
    return
  }

  const content = filteredHistory.value.map(msg => {
    const time = formatTime(msg.createTime)
    const role = msg.role === 'user' ? '用户' : 'AI助手'
    return `[${time}] ${role}: ${msg.content}`
  }).join('\n\n')

  const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `聊天历史记录_${new Date().toISOString().slice(0, 10)}.txt`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)

  ElMessage.success('历史记录已导出')
}

const handleHistoryDialogClose = () => {
  historyDialogVisible.value = false
  historyDateRange.value = []
  filteredHistory.value = []
  historyMessages.value = []
}

const scrollToBottom = () => {
  nextTick(() => {
    if (messageListRef.value) {
      messageListRef.value.scrollTop = messageListRef.value.scrollHeight
    }
  })
}

const loadSessions = async () => {
  try {
    const response = await getChatSessions()
    // 适配若依框架的响应格式 - 通常数据在response.rows中
    const sessionData = response.rows || response.data || []

    // 转换后端数据格式为前端格式
    sessions.value = sessionData.map(session => ({
      id: session.sessionId || session.id,
      title: session.title,
      lastActiveTime: session.lastActiveTime,
      messageCount: session.messageCount || 0
    }))
  } catch (error) {
    console.error('加载会话列表失败:', error)
    ElMessage.error('加载会话列表失败: ' + (error.message || '未知错误'))
  }
}

// 知识库相关方法
const loadKnowledgeBaseList = async () => {
  try {
    const response = await listKnowledgeBase({})
    knowledgeBaseList.value = response.rows || []
  } catch (error) {
    console.error('加载知识库列表失败:', error)
  }
}

const handleKnowledgeBaseChange = (knowledgeBaseId) => {
  if (knowledgeBaseId) {
    const kb = knowledgeBaseList.value.find(item => item.id === knowledgeBaseId)
    if (kb) {
      ElMessage.success(`已选择知识库: ${kb.name}`)
    }
  } else {
    ElMessage.info('已取消知识库选择')
  }
}

// 生命周期
onMounted(async () => {
  await loadSessions()
  await loadKnowledgeBaseList()
  if (sessions.value.length > 0) {
    currentSessionId.value = sessions.value[0].id
    if (currentSessionId.value) {
      await loadChatHistory(currentSessionId.value)
    }
  }
})
</script>

<style lang="scss" scoped>
.ai-chat-container {
  display: flex;
  height: calc(100vh - 84px);
  background: #f5f5f5;
  position: relative;

  .mobile-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    display: none;

    @media (max-width: 768px) {
      display: block;
    }
  }

  .chat-sidebar {
    width: 280px;
    background: white;
    border-right: 1px solid #e4e7ed;
    display: flex;
    flex-direction: column;

    .sidebar-header {
      padding: 16px;
      border-bottom: 1px solid #e4e7ed;

      .new-chat-btn {
        width: 100%;
        height: 36px;
      }
    }

    .session-list {
      flex: 1;
      overflow-y: auto;
      padding: 8px;

      .session-item {
        padding: 12px;
        margin-bottom: 4px;
        border-radius: 8px;
        cursor: pointer;
        position: relative;
        transition: background-color 0.2s;

        &:hover {
          background: #f5f7fa;
        }

        &.active {
          background: #e6f7ff;
          border: 1px solid #91d5ff;
        }

        .session-title {
          font-size: 14px;
          font-weight: 500;
          color: #303133;
          margin-bottom: 4px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .session-time {
          font-size: 12px;
          color: #909399;
        }

        .session-menu {
          position: absolute;
          top: 8px;
          right: 8px;
          opacity: 0;
          transition: opacity 0.2s;
        }

        &:hover .session-menu {
          opacity: 1;
        }
      }
    }
  }

  .chat-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: white;

    .chat-header {
      padding: 16px 24px;
      border-bottom: 1px solid #e4e7ed;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .chat-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 16px;
        font-weight: 500;
        color: #303133;

        .mobile-menu-btn {
          display: none;
          padding: 8px;

          @media (max-width: 768px) {
            display: flex;
          }
        }
      }

      .chat-actions {
        display: flex;
        align-items: center;
        gap: 12px;

        .model-display {
          font-size: 14px;
          color: #606266;
          background: #f5f7fa;
          padding: 6px 12px;
          border-radius: 16px;
          border: 1px solid #e4e7ed;
        }
      }
    }

    .message-list {
      flex: 1;
      overflow-y: auto;
      padding: 24px;

      .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: #909399;

        p {
          margin-top: 16px;
          font-size: 16px;
        }
      }

      .message-item {
        display: flex;
        margin-bottom: 24px;

        &:hover .message-actions {
          opacity: 1;
        }

        &.user-message {
          flex-direction: row-reverse;

          .message-content {
            margin-right: 12px;
            margin-left: 0;

            .message-text {
              background: #409eff;
              color: white;
            }
          }
        }

        &.ai-message {
          .message-content {
            margin-left: 12px;

            .message-text {
              background: #f5f7fa;
              color: #303133;
            }
          }
        }

        .message-avatar {
          flex-shrink: 0;
        }

        .message-content {
          max-width: 70%;

          .message-text {
            padding: 12px 16px;
            border-radius: 12px;
            line-height: 1.5;
            word-wrap: break-word;

            :deep(code) {
              background: rgba(0, 0, 0, 0.1);
              padding: 2px 4px;
              border-radius: 4px;
              font-family: 'Courier New', monospace;
            }
          }

          .message-actions {
            display: flex;
            gap: 4px;
            margin-top: 8px;
            opacity: 0;
            transition: opacity 0.2s;

            .el-button {
              padding: 4px;
              height: auto;
              min-height: auto;

              .el-icon {
                font-size: 14px;
              }
            }
          }

          .streaming-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 8px;
            color: #409eff;
            font-size: 14px;

            .el-icon {
              animation: rotate 2s linear infinite;
            }
          }

          .message-time {
            font-size: 12px;
            color: #909399;
            margin-top: 4px;
            text-align: center;
          }
        }
      }

      .typing-indicator {
        display: flex;
        align-items: center;
        padding: 12px 16px;
        background: #f5f7fa;
        border-radius: 12px;

        span {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background: #909399;
          margin-right: 4px;
          animation: typing 1.4s infinite ease-in-out;

          &:nth-child(1) { animation-delay: -0.32s; }
          &:nth-child(2) { animation-delay: -0.16s; }
          &:nth-child(3) {
            margin-right: 0;
            animation-delay: 0s;
          }
        }
      }
    }

    .input-area {
      padding: 16px 24px;
      border-top: 1px solid #e4e7ed;
      background: #fafafa;

      .input-container {
        display: flex;
        align-items: flex-end;
        gap: 12px;

        .message-input {
          flex: 1;

          :deep(.el-textarea__inner) {
            border-radius: 12px;
            border: 1px solid #dcdfe6;
            padding: 12px 16px;
            resize: none;

            &:focus {
              border-color: #409eff;
            }
          }
        }

        .send-btn {
          height: 40px;
          border-radius: 12px;
          padding: 0 20px;
        }
      }

      .input-tips {
        margin-top: 8px;
        font-size: 12px;
        color: #909399;
        text-align: center;
      }
    }
  }
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

// 消息动画
.message-enter-active {
  transition: all 0.3s ease;
}

.message-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.message-move {
  transition: transform 0.3s ease;
}

// 响应式设计
@media (max-width: 768px) {
  .ai-chat-container {
    .chat-sidebar {
      position: fixed;
      left: 0;
      top: 0;
      height: 100vh;
      z-index: 1000;
      transform: translateX(-100%);
      transition: transform 0.3s ease;
      box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);

      &.mobile-show {
        transform: translateX(0);
      }
    }

    .chat-main {
      width: 100%;

      .message-list {
        padding: 16px;

        .message-item .message-content {
          max-width: 85%;
        }
      }

      .input-area {
        padding: 12px 16px;
      }
    }
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 历史记录对话框样式
.history-content {
  .history-filters {
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .history-list {
    max-height: 500px;
    overflow-y: auto;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    padding: 12px;

    .history-message {
      margin-bottom: 16px;
      padding: 12px;
      border-radius: 8px;
      border: 1px solid #f0f0f0;

      &.user-message {
        background-color: #f0f9ff;
        border-color: #e1f5fe;
      }

      &.assistant-message {
        background-color: #f9f9f9;
        border-color: #e0e0e0;
      }

      .message-header {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        font-size: 12px;
        color: #666;

        .el-icon {
          margin-right: 6px;
        }

        .message-role {
          font-weight: 500;
          margin-right: 12px;
        }

        .message-time {
          margin-left: auto;
          color: #999;
        }
      }

      .message-content {
        line-height: 1.6;
        word-break: break-word;
        white-space: pre-wrap;
      }

      &:last-child {
        margin-bottom: 0;
      }
    }

    .empty-history {
      text-align: center;
      padding: 40px 20px;
      color: #909399;

      .el-icon {
        margin-bottom: 12px;
      }

      p {
        margin: 0;
        font-size: 14px;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
