<template>
  <div class="ai-test-container">
    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <span>AI模块前后端联调测试</span>
        </div>
      </template>
      
      <div class="test-section">
        <h3>1. 测试AI连接</h3>
        <el-button type="primary" @click="testConnection" :loading="connectionLoading">
          测试连接
        </el-button>
        <div v-if="connectionResult" class="result-box">
          <pre>{{ connectionResult }}</pre>
        </div>
      </div>

      <div class="test-section">
        <h3>2. 创建会话测试</h3>
        <el-button type="success" @click="testCreateSession" :loading="sessionLoading">
          创建会话
        </el-button>
        <div v-if="sessionResult" class="result-box">
          <pre>{{ sessionResult }}</pre>
        </div>
      </div>

      <div class="test-section">
        <h3>3. 发送消息测试</h3>
        <el-input
          v-model="testMessage"
          placeholder="输入测试消息"
          style="margin-bottom: 10px;"
        />
        <el-button
          type="warning"
          @click="testSendMessage"
          :loading="messageLoading"
          :disabled="!testMessage.trim()"
        >
          发送消息（自动创建会话）
        </el-button>
        <div v-if="messageResult" class="result-box">
          <pre>{{ messageResult }}</pre>
        </div>
      </div>

      <div class="test-section">
        <h3>4. 获取会话列表</h3>
        <el-button type="info" @click="testGetSessions" :loading="sessionsLoading">
          获取会话列表
        </el-button>
        <div v-if="sessionsResult" class="result-box">
          <pre>{{ sessionsResult }}</pre>
        </div>
      </div>

      <div class="test-section">
        <h3>5. 获取聊天历史</h3>
        <el-button 
          type="primary" 
          @click="testGetHistory" 
          :loading="historyLoading"
          :disabled="!currentSessionId"
        >
          获取聊天历史
        </el-button>
        <div v-if="historyResult" class="result-box">
          <pre>{{ historyResult }}</pre>
        </div>
      </div>

      <div class="test-section">
        <h3>当前会话ID</h3>
        <el-tag v-if="currentSessionId">{{ currentSessionId }}</el-tag>
        <el-tag v-else type="info">未创建会话</el-tag>
      </div>
    </el-card>
  </div>
</template>

<script setup name="AiTest">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import request from '@/utils/request'
import {
  createChatSession,
  sendMessage,
  getChatSessions,
  getChatHistory
} from '@/api/ai/chat'

// 响应式数据
const connectionLoading = ref(false)
const sessionLoading = ref(false)
const messageLoading = ref(false)
const sessionsLoading = ref(false)
const historyLoading = ref(false)

const connectionResult = ref('')
const sessionResult = ref('')
const messageResult = ref('')
const sessionsResult = ref('')
const historyResult = ref('')

const testMessage = ref('你好，这是一个测试消息')
const currentSessionId = ref('')

// 测试AI连接
const testConnection = async () => {
  connectionLoading.value = true
  connectionResult.value = ''
  
  try {
    const response = await request({
      url: '/ai/test/connection',
      method: 'get'
    })
    connectionResult.value = JSON.stringify(response, null, 2)
    ElMessage.success('AI连接测试成功')
  } catch (error) {
    connectionResult.value = JSON.stringify(error.response?.data || error.message, null, 2)
    ElMessage.error('AI连接测试失败')
  } finally {
    connectionLoading.value = false
  }
}

// 测试创建会话
const testCreateSession = async () => {
  sessionLoading.value = true
  sessionResult.value = ''
  
  try {
    const response = await createChatSession({
      title: '测试会话',
      model: 'qwen-plus'
    })
    sessionResult.value = JSON.stringify(response, null, 2)
    
    // 保存会话ID用于后续测试
    const sessionData = response.data || response
    currentSessionId.value = sessionData.sessionId || sessionData.id
    
    ElMessage.success('创建会话成功')
  } catch (error) {
    sessionResult.value = JSON.stringify(error.response?.data || error.message, null, 2)
    ElMessage.error('创建会话失败')
  } finally {
    sessionLoading.value = false
  }
}

// 测试发送消息
const testSendMessage = async () => {
  messageLoading.value = true
  messageResult.value = ''

  try {
    // 如果没有会话ID，使用临时ID让后端自动创建
    const sessionId = currentSessionId.value || 'test-session-' + Date.now()

    const response = await sendMessage({
      sessionId: sessionId,
      message: testMessage.value,
      model: 'qwen-plus'
    })

    // 如果成功，保存会话ID
    if (!currentSessionId.value) {
      currentSessionId.value = sessionId
    }

    messageResult.value = JSON.stringify(response, null, 2)
    ElMessage.success('发送消息成功')
  } catch (error) {
    messageResult.value = JSON.stringify(error.response?.data || error.message, null, 2)
    ElMessage.error('发送消息失败')
  } finally {
    messageLoading.value = false
  }
}

// 测试获取会话列表
const testGetSessions = async () => {
  sessionsLoading.value = true
  sessionsResult.value = ''
  
  try {
    const response = await getChatSessions()
    sessionsResult.value = JSON.stringify(response, null, 2)
    ElMessage.success('获取会话列表成功')
  } catch (error) {
    sessionsResult.value = JSON.stringify(error.response?.data || error.message, null, 2)
    ElMessage.error('获取会话列表失败')
  } finally {
    sessionsLoading.value = false
  }
}

// 测试获取聊天历史
const testGetHistory = async () => {
  historyLoading.value = true
  historyResult.value = ''
  
  try {
    const response = await getChatHistory({ sessionId: currentSessionId.value })
    historyResult.value = JSON.stringify(response, null, 2)
    ElMessage.success('获取聊天历史成功')
  } catch (error) {
    historyResult.value = JSON.stringify(error.response?.data || error.message, null, 2)
    ElMessage.error('获取聊天历史失败')
  } finally {
    historyLoading.value = false
  }
}
</script>

<style lang="scss" scoped>
.ai-test-container {
  padding: 20px;
}

.test-card {
  max-width: 800px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
  font-size: 16px;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fafafa;
  
  h3 {
    margin: 0 0 15px 0;
    color: #303133;
  }
}

.result-box {
  margin-top: 15px;
  padding: 15px;
  background-color: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  max-height: 300px;
  overflow-y: auto;
  
  pre {
    margin: 0;
    white-space: pre-wrap;
    word-wrap: break-word;
    font-size: 12px;
    line-height: 1.4;
  }
}
</style>
