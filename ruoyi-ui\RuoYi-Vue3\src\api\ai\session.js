import request from '@/utils/request'

// 查询AI聊天会话列表
export function listAiChatSession(query) {
  return request({
    url: '/ai/session/list',
    method: 'get',
    params: query
  })
}

// 查询AI聊天会话详细
export function getAiChatSession(sessionId) {
  return request({
    url: '/ai/session/' + sessionId,
    method: 'get'
  })
}

// 新增AI聊天会话
export function addAiChatSession(data) {
  return request({
    url: '/ai/session',
    method: 'post',
    data: data
  })
}

// 修改AI聊天会话
export function updateAiChatSession(data) {
  return request({
    url: '/ai/session',
    method: 'put',
    data: data
  })
}

// 删除AI聊天会话
export function delAiChatSession(sessionId) {
  return request({
    url: '/ai/session/' + sessionId,
    method: 'delete'
  })
}
