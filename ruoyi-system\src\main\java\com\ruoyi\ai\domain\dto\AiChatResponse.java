package com.ruoyi.ai.domain.dto;

/**
 * AI聊天响应DTO
 * 
 * <AUTHOR>
 * @date 2024-07-09
 */
public class AiChatResponse
{
    /** 消息ID */
    private String messageId;

    /** 响应内容 */
    private String content;

    /** 响应时间（毫秒） */
    private Long responseTime;

    /** Token数量 */
    private Long tokenCount;

    /** 是否完成 */
    private Boolean finished = true;

    public AiChatResponse() {
    }

    public AiChatResponse(String messageId, String content) {
        this.messageId = messageId;
        this.content = content;
    }

    public AiChatResponse(String messageId, String content, Long responseTime) {
        this.messageId = messageId;
        this.content = content;
        this.responseTime = responseTime;
    }

    public String getMessageId() {
        return messageId;
    }

    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Long getResponseTime() {
        return responseTime;
    }

    public void setResponseTime(Long responseTime) {
        this.responseTime = responseTime;
    }

    public Long getTokenCount() {
        return tokenCount;
    }

    public void setTokenCount(Long tokenCount) {
        this.tokenCount = tokenCount;
    }

    public Boolean getFinished() {
        return finished;
    }

    public void setFinished(Boolean finished) {
        this.finished = finished;
    }

    @Override
    public String toString() {
        return "AiChatResponse{" +
                "messageId='" + messageId + '\'' +
                ", content='" + content + '\'' +
                ", responseTime=" + responseTime +
                ", tokenCount=" + tokenCount +
                ", finished=" + finished +
                '}';
    }
}
